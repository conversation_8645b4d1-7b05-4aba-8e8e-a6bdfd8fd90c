
-- Make email field nullable in clients table
ALTER TABLE public.clients ALTER COLUMN email DROP NOT NULL;

-- Create storage bucket for client documents
INSERT INTO storage.buckets (id, name, public) VALUES ('client-documents', 'client-documents', true);

-- Create storage policies for client documents
CREATE POLICY "Authenticated users can upload client documents" ON storage.objects 
FOR INSERT WITH CHECK (bucket_id = 'client-documents' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view client documents" ON storage.objects 
FOR SELECT USING (bucket_id = 'client-documents' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update client documents" ON storage.objects 
FOR UPDATE USING (bucket_id = 'client-documents' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete client documents" ON storage.objects 
FOR DELETE USING (bucket_id = 'client-documents' AND auth.role() = 'authenticated');
