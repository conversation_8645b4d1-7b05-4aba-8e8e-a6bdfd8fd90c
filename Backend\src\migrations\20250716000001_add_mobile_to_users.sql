-- Add mobile field to users table for phone-based authentication
ALTER TABLE public.users
ADD COLUMN mobile VARCHAR(20) UNIQUE;

-- Add index for better performance on mobile lookups
CREATE INDEX idx_users_mobile ON public.users(mobile);

-- Update the handle_new_user function to also store mobile number
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  default_role_id UUID;
BEGIN
  -- Get the default Employee role ID
  SELECT id INTO default_role_id FROM public.roles WHERE name = 'Employee' LIMIT 1;

  -- Insert into users table
  INSERT INTO public.users (id, username, role_id, mobile)
  VALUES (
    NEW.id,
    COALESCE(NEW.email, NEW.phone),
    default_role_id,
    NEW.phone
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get email by phone number for authentication
CREATE OR REPLACE FUNCTION public.get_email_by_phone(phone_number TEXT)
RETURNS TEXT AS $$
DECLARE
  user_email TEXT;
BEGIN
  -- Get the email from auth.users by joining with public.users
  SELECT au.email INTO user_email
  FROM public.users u
  JOIN auth.users au ON u.id = au.id
  WHERE u.mobile = phone_number
    AND u.is_deleted = false;

  RETURN user_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;
