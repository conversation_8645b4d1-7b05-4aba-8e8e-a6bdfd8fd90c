import { Request, Response } from 'express';
import sequelize from '../config/database';
import { QueryTypes } from 'sequelize';

export const getAllUsers = async (req: Request, res: Response): Promise<void> => {
  try {
    // Use raw query with proper typing
    const users = await sequelize.query('SELECT * FROM users', {
      type: QueryTypes.SELECT
    });
    
    res.status(200).json(users);
  } catch (error: any) {
    console.error('Error fetching users:', error);
    res.status(500).json({ 
      message: 'Error fetching users', 
      error: error.message || 'Unknown error'
    });
  }
};