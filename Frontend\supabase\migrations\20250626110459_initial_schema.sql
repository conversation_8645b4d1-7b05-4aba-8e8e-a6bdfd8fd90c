
-- Create roles table
CREATE TABLE public.roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default roles
INSERT INTO public.roles (name, description) VALUES 
('Admin', 'Full access to all features'),
('Employee', 'Limited access for daily operations'),
('Viewer', 'Read-only access');

-- Create users table (extends auth.users)
CREATE TABLE public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username VARCHAR(50) UNIQUE,
  role_id UUID REFERENCES public.roles(id),
  is_deleted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create clients table
CREATE TABLE public.clients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100)  NULL,
  email VARCHAR(255) UNIQUE NULL,
  mobile_number VARCHAR(20) UNIQUE NOT NULL,
  sb_account_number VARCHAR(11) UNIQUE NOT NULL,
  cif_id VARCHAR(50) UNIQUE,
  village VARCHAR(100) NULL,
  city VARCHAR(100) NULL,
  state VARCHAR(100) NULL,
  pincode VARCHAR(10) NULL,
  address TEXT NULL,
  care_of VARCHAR(100),
  contact_person2 VARCHAR(100),
  aadhar_number VARCHAR(12) UNIQUE NULL,
  pan_card_number VARCHAR(10) UNIQUE NULL,
  client_photo_url TEXT,
  aadhar_photo_url TEXT,
  pan_photo_url TEXT,
  is_deleted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create nominees table
CREATE TABLE public.nominees (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  relation VARCHAR(50) NOT NULL,
  birthdate DATE,
  document_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create schemes table
CREATE TABLE public.schemes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  scheme_code VARCHAR(50) UNIQUE NOT NULL,
  interest_rate DECIMAL(5,2) NOT NULL,
  interest_type VARCHAR(20) NOT NULL CHECK (interest_type IN ('simple', 'compound')),
  compounding_frequency VARCHAR(20) DEFAULT 'annually',
  payout_type VARCHAR(20) NOT NULL,
  tenure_months INTEGER NOT NULL,
  min_amount DECIMAL(15,2) NOT NULL,
  max_amount DECIMAL(15,2),
  lock_in_period_months INTEGER DEFAULT 0,
  commission_percentage DECIMAL(5,2) DEFAULT 0,
  supports_sip BOOLEAN DEFAULT FALSE,
  min_sip_amount DECIMAL(15,2),
  max_sip_amount DECIMAL(15,2),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create investments table
CREATE TABLE public.investments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES public.clients(id) NOT NULL,
  scheme_id UUID REFERENCES public.schemes(id) NOT NULL,
  nominee_id UUID REFERENCES public.nominees(id),
  second_applicant_id UUID REFERENCES public.clients(id),
  amount DECIMAL(15,2) NOT NULL,
  investment_date DATE NOT NULL,
  start_date DATE NOT NULL,
  maturity_date DATE NOT NULL,
  maturity_amount DECIMAL(15,2) NOT NULL,
  actual_profit DECIMAL(15,2) NOT NULL,
  tds_amount DECIMAL(15,2) DEFAULT 0,
  investment_type VARCHAR(20) DEFAULT 'lumpsum' CHECK (investment_type IN ('lumpsum', 'sip')),
  sip_frequency VARCHAR(20),
  sip_amount DECIMAL(15,2),
  sip_next_due DATE,
  sip_installments INTEGER,
  reinvestment_source_id UUID REFERENCES public.investments(id),
  remark TEXT,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'matured', 'withdrawn')),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transactions table
CREATE TABLE public.transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  investment_id UUID REFERENCES public.investments(id) NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  transaction_date DATE NOT NULL,
  amount_type VARCHAR(20) NOT NULL CHECK (amount_type IN ('investment', 'interest', 'maturity', 'withdrawal' ,'reinvestment', 'interest_payout', 'maturity_payout', 'penalty', 'commission')),
  reference_number VARCHAR(100) UNIQUE NOT NULL, 
  remark TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create alerts table
CREATE TABLE public.alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  investment_id UUID REFERENCES public.investments(id) NULL,
  alert_type VARCHAR(50) NOT NULL,
  alert_date DATE NOT NULL,
  message TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
  channel VARCHAR(20) NOT NULL CHECK (channel IN ('email', 'sms', 'whatsapp')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification_settings table
CREATE TABLE public.notification_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  smtp_host VARCHAR(255),
  smtp_port INTEGER DEFAULT 587,
  smtp_username VARCHAR(255),
  smtp_password TEXT,
  default_email_from VARCHAR(255),
  email_enabled BOOLEAN DEFAULT TRUE,
  sms_enabled BOOLEAN DEFAULT FALSE,
  whatsapp_enabled BOOLEAN DEFAULT FALSE,
  alert_days_before INTEGER DEFAULT 7,
  notification_time TIME DEFAULT '09:00:00',
  tds_percentage DECIMAL(5,2) DEFAULT 10.00,
  default_commission DECIMAL(5,2) DEFAULT 0.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default notification settings
INSERT INTO public.notification_settings (alert_days_before, tds_percentage) VALUES (7, 10.00);

-- Create role_permissions table
CREATE TABLE public.role_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE,
  module VARCHAR(50) NOT NULL,
  can_view BOOLEAN DEFAULT FALSE,
  can_add BOOLEAN DEFAULT FALSE,
  can_edit BOOLEAN DEFAULT FALSE,
  can_delete BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_id, module)
);

-- Insert default permissions for Admin role
INSERT INTO public.role_permissions (role_id, module, can_view, can_add, can_edit, can_delete)
SELECT r.id, module, true, true, true, true
FROM public.roles r
CROSS JOIN (VALUES 
  ('dashboard'),
  ('clients'),
  ('investments'),
  ('schemes'),
  ('reports'),
  ('settings'),
  ('calculator'),
  ('alerts')
) AS modules(module)
WHERE r.name = 'Admin';

-- Insert default permissions for Employee role
INSERT INTO public.role_permissions (role_id, module, can_view, can_add, can_edit, can_delete)
SELECT r.id, module, true, true, true, false
FROM public.roles r
CROSS JOIN (VALUES 
  ('dashboard'),
  ('clients'),
  ('investments'),
  ('calculator'),
  ('alerts')
) AS modules(module)
WHERE r.name = 'Employee'
UNION ALL
SELECT r.id, module, true, false, false, false
FROM public.roles r
CROSS JOIN (VALUES 
  ('schemes'),
  ('reports')
) AS modules(module)
WHERE r.name = 'Employee';

-- Insert default permissions for Viewer role
INSERT INTO public.role_permissions (role_id, module, can_view, can_add, can_edit, can_delete)
SELECT r.id, module, true, false, false, false
FROM public.roles r
CROSS JOIN (VALUES 
  ('dashboard'),
  ('clients'),
  ('investments'),
  ('schemes'),
  ('reports'),
  ('calculator')
) AS modules(module)
WHERE r.name = 'Viewer';

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.nominees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schemes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- Create security definer function to get user role
CREATE OR REPLACE FUNCTION public.get_user_role()
RETURNS TEXT AS $$
  SELECT r.name FROM public.users u 
  JOIN public.roles r ON u.role_id = r.id 
  WHERE u.id = auth.uid();
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- Create RLS policies for authenticated users
CREATE POLICY "Authenticated users can access all data" ON public.users FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.clients FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.nominees FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.schemes FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.investments FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.transactions FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.alerts FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.notification_settings FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.roles FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can access all data" ON public.role_permissions FOR ALL USING (auth.role() = 'authenticated');

-- Create storage bucket for documents
INSERT INTO storage.buckets (id, name, public) VALUES ('documents', 'documents', true);

-- Create storage policy for documents
CREATE POLICY "Authenticated users can upload documents" ON storage.objects 
FOR INSERT WITH CHECK (bucket_id = 'documents' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view documents" ON storage.objects 
FOR SELECT USING (bucket_id = 'documents' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update documents" ON storage.objects 
FOR UPDATE USING (bucket_id = 'documents' AND auth.role() = 'authenticated');

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  default_role_id UUID;
BEGIN
  -- Get the default Employee role ID
  SELECT id INTO default_role_id FROM public.roles WHERE name = 'Employee' LIMIT 1;
  
  -- Insert into users table
  INSERT INTO public.users (id, username, role_id)
  VALUES (
    NEW.id,
    NEW.email,
    default_role_id
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
