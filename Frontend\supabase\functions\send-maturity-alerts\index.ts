import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS"
};

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    // Get notification settings
    const { data: settings } = await supabase
      .from('notification_settings')
      .select('alert_days_before, email_enabled')
      .single();

    if (!settings?.email_enabled) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Email notifications disabled' 
      }), {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

    const alertDaysBefore = settings.alert_days_before || 7;
    const alertDate = new Date();
    alertDate.setDate(alertDate.getDate() + alertDaysBefore);
    const alertDateStr = alertDate.toISOString().split('T')[0];

    // Get investments maturing on alert date
    const { data: investments, error } = await supabase
      .from('investments')
      .select(`
        id, scheme_name, amount, maturity_amount, maturity_date,
        clients:clients!investments_client_id_fkey(id, first_name, last_name, email)
      `)
      .eq('status', 'active')
      .eq('maturity_date', alertDateStr);

    if (error) throw error;

    const results = [];
    
    // Get notification settings from database
    const { data: emailSettings } = await supabase
      .from('notification_settings')
      .select('sendgrid_api_key, sendgrid_sender_email')
      .single();

    const sendGridApiKey = emailSettings?.sendgrid_api_key;
    const fromEmail = emailSettings?.sendgrid_sender_email;

    if (!sendGridApiKey || !fromEmail) {
      return new Response(JSON.stringify({
        success: false,
        message: 'SendGrid configuration missing in database settings'
      }), {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

    for (const investment of investments || []) {
      if (!investment.clients?.email) {
        await logAlert(supabase, investment.id, 'email', 'failed', 'No email address');
        continue;
      }

      try {
        const emailContent = generateMaturityAlertEmail(investment, alertDaysBefore);
        
        const sendGridPayload = {
          personalizations: [{
            to: [{
              email: investment.clients.email,
              name: `${investment.clients.first_name} ${investment.clients.last_name}`
            }]
          }],
          from: {
            email: fromEmail,
            name: "Investment Management Team"
          },
          subject: `🔔 Investment Maturity Alert - ${investment.scheme_name}`,
          content: [{
            type: "text/html",
            value: emailContent
          }]
        };

        const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${sendGridApiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(sendGridPayload)
        });

        if (response.ok) {
          await logAlert(supabase, investment.id, 'email', 'sent', 'Maturity alert sent successfully');
          results.push({ investment_id: investment.id, status: 'sent' });
        } else {
          const errorText = await response.text();
          await logAlert(supabase, investment.id, 'email', 'failed', `SendGrid error: ${errorText}`);
          results.push({ investment_id: investment.id, status: 'failed', error: errorText });
        }
      } catch (emailError: any) {
        await logAlert(supabase, investment.id, 'email', 'failed', emailError.message);
        results.push({ investment_id: investment.id, status: 'failed', error: emailError.message });
      }
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Processed ${investments?.length || 0} maturity alerts`,
      results
    }), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders }
    });

  } catch (error: any) {
    console.error('Cron job error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json", ...corsHeaders }
    });
  }
};

async function logAlert(supabase: any, investmentId: string, channel: string, status: string, message: string) {
  await supabase.from('alerts').insert({
    investment_id: investmentId,
    alert_type: 'maturity_reminder',
    channel,
    status,
    message,
    alert_date: new Date().toISOString().split('T')[0]
  });
}

function generateMaturityAlertEmail(investment: any, daysBefore: number) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Investment Maturity Alert</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; }
        .email-wrapper { max-width: 650px; margin: 0 auto; background: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { font-size: 28px; font-weight: 700; margin-bottom: 8px; }
        .content { padding: 40px 30px; }
        .alert-badge { background: #fef3c7; color: #92400e; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; display: inline-block; margin-bottom: 20px; }
        .investment-card { background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-radius: 12px; padding: 25px; margin: 25px 0; border-left: 5px solid #f59e0b; }
        .detail-item { background: #ffffff; padding: 16px; border-radius: 8px; margin-bottom: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .detail-label { font-size: 12px; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px; }
        .detail-value { font-size: 16px; font-weight: 600; color: #1f2937; }
        .amount-highlight { color: #059669; font-size: 18px; }
        .cta-section { text-align: center; margin: 30px 0; padding: 25px; background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); border-radius: 12px; }
        .footer { background: #1f2937; padding: 30px; text-align: center; color: #9ca3af; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        <div class="header">
          <h1>🔔 Investment Maturity Alert</h1>
          <p>Your investment is maturing soon</p>
        </div>
        
        <div class="content">
          <div class="alert-badge">⏰ Maturing in ${daysBefore} days</div>
          
          <div style="font-size: 18px; color: #1f2937; margin-bottom: 20px;">
            Dear ${investment.clients.first_name} ${investment.clients.last_name},
          </div>
          
          <div style="font-size: 16px; color: #6b7280; margin-bottom: 30px; line-height: 1.6;">
            This is a friendly reminder that your investment is approaching its maturity date. Please review the details below:
          </div>
          
          <div class="investment-card">
            <div style="font-size: 20px; font-weight: 600; color: #1f2937; margin-bottom: 20px;">
              📈 Investment Details
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Scheme Name</div>
              <div class="detail-value">${investment.scheme_name}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Investment Amount</div>
              <div class="detail-value amount-highlight">₹${investment.amount.toLocaleString()}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Maturity Amount</div>
              <div class="detail-value amount-highlight">₹${investment.maturity_amount.toLocaleString()}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Maturity Date</div>
              <div class="detail-value">${new Date(investment.maturity_date).toLocaleDateString('en-IN')}</div>
            </div>
          </div>
          
          <div class="cta-section">
            <div style="font-size: 16px; color: #1e40af; margin-bottom: 15px; font-weight: 500;">
              🎉 Congratulations! Your investment has grown successfully.
            </div>
            <div style="font-size: 14px; color: #1e40af;">
              Please contact us to discuss your maturity options or reinvestment opportunities.
            </div>
          </div>
          
          <div style="margin-top: 30px; font-size: 16px; color: #1f2937; line-height: 1.6;">
            Best regards,<br>
            <strong style="color: #f59e0b;">Investment Management Team</strong>
          </div>
        </div>
        
        <div class="footer">
          <div style="color: #ffffff; font-weight: 600; font-size: 16px; margin-bottom: 8px;">Investment Pro</div>
          This is an automated maturity alert. For assistance, please contact our support team.
        </div>
      </div>
    </body>
    </html>
  `;
}

serve(handler);