#!/bin/bash

# Test Setup Script
# This script validates the Docker setup without starting all services

echo "🧪 Testing Full Stack Application Setup..."

# Check if required files exist
echo "📋 Checking required files..."

required_files=(
    ".env"
    "docker-compose.yml"
    "Frontend/Dockerfile"
    "Backend/Dockerfile"
    "Frontend/package.json"
    "Backend/package.json"
)

missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ Missing required files:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

echo "✅ All required files present"

# Check if Docker is running
echo "🐳 Checking Docker..."
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "✅ Docker is running"

# Validate docker-compose file
echo "📝 Validating docker-compose.yml..."
if ! docker-compose config > /dev/null 2>&1; then
    echo "❌ docker-compose.yml has syntax errors"
    docker-compose config
    exit 1
fi

echo "✅ docker-compose.yml is valid"

# Check if ports are available
echo "🔌 Checking port availability..."
ports=(3000 3001 3002 8000 8443 5432 6543)

for port in "${ports[@]}"; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        echo "⚠️  Port $port is already in use"
    fi
done

# Test building images (without starting)
echo "🔨 Testing Docker image builds..."

echo "   Building Frontend image..."
if ! docker build -t test-frontend ./Frontend > /dev/null 2>&1; then
    echo "❌ Frontend Docker build failed"
    exit 1
fi

echo "   Building Backend image..."
if ! docker build -t test-backend ./Backend > /dev/null 2>&1; then
    echo "❌ Backend Docker build failed"
    exit 1
fi

echo "✅ Docker images build successfully"

# Clean up test images
echo "🧹 Cleaning up test images..."
docker rmi test-frontend test-backend > /dev/null 2>&1

echo ""
echo "✅ Setup validation completed successfully!"
echo ""
echo "🚀 Your application is ready to start:"
echo "   ./start.sh    (Linux/Mac)"
echo "   start.bat     (Windows)"
echo ""
echo "🔧 For development mode:"
echo "   ./dev.sh      (Linux/Mac)"
echo "   dev.bat       (Windows)"
