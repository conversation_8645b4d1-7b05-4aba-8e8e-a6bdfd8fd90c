-- Update payment modes to include new options and make reference_number nullable

-- First, update the payment_mode constraint to include new payment modes
ALTER TABLE public.transactions
DROP CONSTRAINT IF EXISTS transactions_payment_mode_check;

ALTER TABLE public.transactions
ADD CONSTRAINT transactions_payment_mode_check
CHECK (payment_mode IN ('sb_account', 'cheque', 'ecs', 'cash', 'rtgs/neft'));

-- Update existing 'maturity_payout' records to 'withdrawal' (withdrawal is already allowed in original schema)
UPDATE public.transactions
SET amount_type = 'withdrawal'
WHERE amount_type = 'maturity_payout';

-- Update existing 'transferred' investment status to 'withdrawn'
UPDATE public.investments
SET status = 'withdrawn'
WHERE status = 'transferred';

-- Make reference_number nullable for cash payments
ALTER TABLE public.transactions
ALTER COLUMN reference_number DROP NOT NULL;

-- Update the unique constraint on reference_number to allow nulls
ALTER TABLE public.transactions
DROP CONSTRAINT IF EXISTS transactions_reference_number_key;

-- Create a unique constraint that allows multiple null values
CREATE UNIQUE INDEX transactions_reference_number_unique_idx
ON public.transactions (reference_number)
WHERE reference_number IS NOT NULL;
