
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  compounding_frequency: string;
  payout_type: string;
  tenure_months: number;
  lock_in_period_months: number;
  min_amount: number;
  max_amount: number;
  commission_percentage: number;
  supports_sip: boolean;
  min_sip_amount: number;
  max_sip_amount: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const SchemeDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [scheme, setScheme] = useState<Scheme | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchSchemeDetails();
    }
  }, [id]);

  const fetchSchemeDetails = async () => {
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      setScheme(data);
    } catch (error) {
      console.error('Error fetching scheme details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch scheme details",
        variant: "destructive",
      });
      navigate('/schemes');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading scheme details...</div>
      </div>
    );
  }

  if (!scheme) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Scheme not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 ">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/schemes')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back to Schemes</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">
              {scheme.name}
            </h1>
            <p className="text-gray-600">Code: {scheme.scheme_code}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Badge variant={scheme.is_active ? "default" : "secondary"}>
            {scheme.is_active ? "Active" : "Inactive"}
          </Badge>
          <Button
            onClick={() => navigate(`/schemes/${id}/edit`)}
            size="sm"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Scheme
          </Button>
        </div>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Scheme Name</label>
              <p className="text-base font-medium">{scheme.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Scheme Code</label>
              <p className="text-base">{scheme.scheme_code}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Interest Rate</label>
              <p className="text-base">{scheme.interest_rate}% per annum</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Interest Type</label>
              <p className="text-base capitalize">{scheme.interest_type}</p>
            </div>
            {scheme.interest_type === 'compound' && scheme.compounding_frequency && (
              <div>
                <label className="text-sm font-medium text-gray-500">Compounding Frequency</label>
                <p className="text-base capitalize">{scheme.compounding_frequency}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-gray-500">Payout Type</label>
              <p className="text-base capitalize">{scheme.payout_type}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Terms & Amount */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Terms & Amount</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Tenure</label>
              <p className="text-base">{scheme.tenure_months} months</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Lock-in Period</label>
              <p className="text-base">{scheme.lock_in_period_months} months</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Minimum Amount</label>
              <p className="text-base">₹{scheme.min_amount.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Maximum Amount</label>
              <p className="text-base">
                {scheme.max_amount ? `₹${scheme.max_amount.toLocaleString()}` : 'No limit'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Commission Percentage</label>
              <p className="text-base">{scheme.commission_percentage}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SIP Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">SIP Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Badge variant={scheme.supports_sip ? "default" : "secondary"}>
              {scheme.supports_sip ? "SIP Supported" : "SIP Not Supported"}
            </Badge>
          </div>

          {scheme.supports_sip && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Minimum SIP Amount</label>
                <p className="text-base">
                  {scheme.min_sip_amount ? `₹${scheme.min_sip_amount.toLocaleString()}` : 'Not specified'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Maximum SIP Amount</label>
                <p className="text-base">
                  {scheme.max_sip_amount ? `₹${scheme.max_sip_amount.toLocaleString()}` : 'Not specified'}
                </p>
              </div>
            </div>
          )}

          {!scheme.supports_sip && (
            <p className="text-gray-500">This scheme does not support Systematic Investment Plan (SIP).</p>
          )}
        </CardContent>
      </Card>

      {/* Status & Dates */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Status & Dates</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <div className="mt-1">
                <Badge variant={scheme.is_active ? "default" : "secondary"}>
                  {scheme.is_active ? "Active" : "Inactive"}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Created On</label>
              <p className="text-base">{new Date(scheme.created_at).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-base">{new Date(scheme.updated_at).toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SchemeDetail;
