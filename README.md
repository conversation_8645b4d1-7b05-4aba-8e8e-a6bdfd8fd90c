# Full Stack Application with Local Supabase

This project provides a complete full-stack application setup with local Supabase, React frontend, and Node.js backend, all orchestrated with <PERSON><PERSON>.

## 🏗️ Architecture

- **Frontend**: React + Vite + TypeScript + Tailwind CSS + shadcn/ui
- **Backend**: Node.js + Express + TypeScript
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Real-time**: Supabase Realtime
- **API**: Supabase REST API + PostgREST
- **Admin Panel**: Supabase Studio

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Git (for cloning)

### 1. Environment Setup

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Update the `.env` file with your configuration:
   - Change default passwords and secrets
   - Adjust ports if needed
   - Configure SMTP settings for email functionality

### 2. Start the Application

**For Windows:**
```cmd
start.bat
```

**For Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

**For Development Mode:**
```cmd
dev.bat    # Windows
./dev.sh   # Linux/Mac
```

### 3. Access Your Applications

Once started, you can access:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3002
- **Supabase Studio**: http://localhost:3001
- **Supabase API**: http://localhost:8000

## 📁 Project Structure

```
├── Frontend/              # React frontend application
│   ├── src/              # Source code
│   ├── Dockerfile        # Frontend Docker configuration
│   └── nginx.conf        # Nginx configuration
├── Backend/              # Node.js backend application
│   ├── src/              # Source code
│   └── Dockerfile        # Backend Docker configuration
├── docker/               # Supabase Docker configuration
│   ├── volumes/          # Persistent data volumes
│   └── dev/              # Development-specific configs
├── docker-compose.yml    # Main Docker Compose file
├── .env                  # Environment variables
└── scripts/              # Utility scripts
```

## 🛠️ Development

### Running Individual Services

```bash
# Start only Supabase services
docker-compose up db auth rest storage kong studio

# Start only your application
docker-compose up frontend backend

# View logs for specific service
docker-compose logs -f frontend
```

### Database Management

```bash
# Access PostgreSQL directly
docker-compose exec db psql -U postgres

# Reset database (removes all data)
./reset.sh  # or reset.bat on Windows
```

### Environment Variables

Key environment variables in `.env`:

```env
# Database
POSTGRES_PASSWORD=your-secure-password
POSTGRES_HOST=db
POSTGRES_PORT=5432
POSTGRES_DB=postgres

# Supabase
JWT_SECRET=your-jwt-secret-32-chars-minimum
ANON_KEY=your-anon-key
SERVICE_ROLE_KEY=your-service-role-key

# Application
VITE_SUPABASE_URL=http://localhost:8000
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## 🔧 Configuration

### Frontend Configuration

The frontend is configured to connect to the local Supabase instance. Key files:

- `Frontend/src/integrations/supabase/client.ts` - Supabase client configuration
- `Frontend/vite.config.ts` - Vite configuration
- `Frontend/nginx.conf` - Production nginx configuration

### Backend Configuration

The backend connects to both the local Supabase database and API:

- Database connection via PostgreSQL
- Supabase client for auth and API operations
- Express server with CORS configured for frontend

## 📦 Deployment

### Production Deployment

1. Update environment variables for production
2. Build and deploy using Docker:

```bash
# Build for production
docker-compose build

# Deploy to your server
docker-compose up -d
```

### Environment-Specific Configurations

- **Development**: Uses `dev.sh`/`dev.bat` with additional development services
- **Production**: Uses `start.sh`/`start.bat` with optimized configurations

## 🛑 Management Commands

### Stop Services
```bash
./stop.sh    # Linux/Mac
stop.bat     # Windows
```

### Reset Everything (⚠️ Removes all data)
```bash
./reset.sh   # Linux/Mac
reset.bat    # Windows
```

### View Service Status
```bash
docker-compose ps
```

### View Logs
```bash
docker-compose logs -f [service-name]
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Conflicts**: Check if ports 3000, 3001, 3002, 8000 are available
2. **Docker Issues**: Ensure Docker is running and has sufficient resources
3. **Environment Variables**: Verify `.env` file is properly configured
4. **Database Connection**: Check if database service is healthy

### Health Checks

```bash
# Check all services
docker-compose ps

# Check specific service logs
docker-compose logs [service-name]

# Check database connectivity
docker-compose exec db pg_isready -U postgres
```

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [React Documentation](https://reactjs.org/docs)
- [Node.js Documentation](https://nodejs.org/docs)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
