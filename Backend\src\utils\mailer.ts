import nodemailer from 'nodemailer';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Create a function to get transporter with settings from database
export const getTransporter = async () => {
  try {
    // Get settings from database
    const { data: settings } = await supabase
      .from('notification_settings')
      .select('smtp_host, smtp_port, smtp_username, smtp_password, email_enabled')
      .single();
    
    if (!settings || !settings.email_enabled) {
      throw new Error('Email is disabled or settings not found');
    }
    
    return nodemailer.createTransport({
      host: settings.smtp_host || 'smtp.gmail.com',
      port: settings.smtp_port || 587,
      secure: false,
      auth: {
        user: settings.smtp_username || process.env.EMAIL_USER,
        pass: settings.smtp_password || process.env.EMAIL_PASS,
      },
    });
  } catch (error) {
    // Fallback to environment variables
    return nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
  }
};

// For backward compatibility
// export const transporter = nodemailer.createTransport({
//   host: 'smtp.gmail.com',
//   port: 587,
//   secure: false,
//   auth: {
//     user: process.env.EMAIL_USER,
//     pass: process.env.EMAIL_PASS,
//   },
// });
