-- Migration: 20250718000001_fix_family_hash_grouping.sql
-- Fixes the family hash grouping issue where same client combinations appear as different families

-- Fix the get_client_units_summary function to properly group by client_unit_hash
-- This ensures that "Me and My Wife" and "My Wife and Me" are treated as the same family unit
CREATE OR REPLACE FUNCTION public.get_client_units_summary()
RETURNS TABLE (
  client_unit_hash VARCHAR(64),
  primary_client_name TEXT,
  secondary_client_name TEXT,
  primary_client_mobile TEXT,
  primary_client_email VARCHAR(255),
  unit_type TEXT,
  total_investments BIGINT,
  total_investment_amount DECIMAL,
  active_investments BIGINT,
  active_amount DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  WITH client_unit_data AS (
    SELECT 
      i.client_unit_hash,
      -- Always use the client with the smaller UUID as primary for consistent display
      CASE 
        WHEN i.second_applicant_id IS NULL THEN i.client_id
        WHEN i.client_id < COALESCE(i.second_applicant_id, i.client_id) THEN i.client_id
        ELSE i.second_applicant_id
      END as display_primary_id,
      CASE 
        WHEN i.second_applicant_id IS NULL THEN NULL
        WHEN i.client_id < COALESCE(i.second_applicant_id, i.client_id) THEN i.second_applicant_id
        ELSE i.client_id
      END as display_secondary_id,
      i.id as investment_id,
      i.amount,
      i.status
    FROM public.investments i
    WHERE EXISTS (
      SELECT 1 FROM public.clients c WHERE c.id = i.client_id AND c.is_deleted = false
    )
  )
  SELECT 
    cud.client_unit_hash,
    CONCAT(pc.first_name, ' ', pc.last_name) as primary_client_name,
    CASE 
      WHEN sc.id IS NOT NULL THEN CONCAT(sc.first_name, ' ', sc.last_name)
      ELSE NULL
    END as secondary_client_name,
    pc.mobile_number::TEXT as primary_client_mobile,
    pc.email as primary_client_email,
    CASE 
      WHEN sc.id IS NOT NULL THEN 'Joint'
      ELSE 'Individual'
    END as unit_type,
    COUNT(cud.investment_id) as total_investments,
    COALESCE(SUM(cud.amount), 0) as total_investment_amount,
    COUNT(cud.investment_id) FILTER (WHERE cud.status = 'active') as active_investments,
    COALESCE(SUM(cud.amount) FILTER (WHERE cud.status = 'active'), 0) as active_amount
  FROM client_unit_data cud
  JOIN public.clients pc ON cud.display_primary_id = pc.id
  LEFT JOIN public.clients sc ON cud.display_secondary_id = sc.id
  WHERE pc.is_deleted = false
  GROUP BY cud.client_unit_hash, pc.first_name, pc.last_name, pc.mobile_number, pc.email, sc.first_name, sc.last_name, sc.id
  ORDER BY total_investment_amount DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment to document the fix
COMMENT ON FUNCTION public.get_client_units_summary() IS 'Returns summary of all client units with consistent grouping by client_unit_hash to prevent duplicate family entries';
