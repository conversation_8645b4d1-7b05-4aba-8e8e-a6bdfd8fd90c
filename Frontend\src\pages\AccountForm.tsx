
import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import ClientSelector from '@/components/investment/ClientSelector';

const accountFormSchema = z.object({
  type: z.enum(['new', 'existing']),
  account_type: z.enum(['single', 'joint']),
  sb_account_number: z.string()
    .min(1, 'Account number is required')
    .length(11, 'Account number must be exactly 11 digits')
    .regex(/^\d{11}$/, 'Account number must contain only digits'),
  opening_balance: z.string().optional(),
  status: z.enum(['active', 'closed', 'suspended']),
  opened_at: z.string().optional(),
  remarks: z.string().optional(),
});

type AccountFormData = z.infer<typeof accountFormSchema>;

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  village: string;
  cif_id: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  aadhar_number?: string;
  pan_card_number?: string;
  contact_person2?: string;
  client_photo_url?: string;
  aadhar_photo_url?: string;
  pan_photo_url?: string;
  care_of?: string;
  is_deleted?: boolean;
  created_at?: string;
  updated_at?: string;
}

const AccountForm: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [primaryClient, setPrimaryClient] = useState<Client | null>(null);
  const [secondaryClient, setSecondaryClient] = useState<Client | null>(null);
  const [accountNumberError, setAccountNumberError] = useState<string | null>(null);
  const [isCheckingAccount, setIsCheckingAccount] = useState(false);

  const form = useForm<AccountFormData>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      type: 'new',
      account_type: 'single',
      sb_account_number: '',
      opening_balance: '',
      status: 'active',
      opened_at: '',
      remarks: '',
    },
  });

  const typeValue = form.watch('type');
  const accountTypeValue = form.watch('account_type');

  const validateAccountNumber = async (accountNumber: string) => {
    if (!accountNumber.trim()) {
      setAccountNumberError(null);
      return;
    }

    if (accountNumber.length !== 11) {
      setAccountNumberError('Account number must be exactly 11 digits');
      return;
    }

    if (!/^\d{11}$/.test(accountNumber)) {
      setAccountNumberError('Account number must contain only digits');
      return;
    }

    setIsCheckingAccount(true);
    try {
      const { data: existingAccount } = await supabase
        .from('sb_accounts')
        .select('id')
        .eq('sb_account_number', accountNumber)
        .eq('is_deleted', false)
        .single();

      if (existingAccount) {
        setAccountNumberError('Account number already exists');
      } else {
        setAccountNumberError(null);
      }
    } catch (error) {
      if (error.code !== 'PGRST116') {
        setAccountNumberError('Error checking account number');
      } else {
        setAccountNumberError(null);
      }
    } finally {
      setIsCheckingAccount(false);
    }
  };

  const onSubmit = async (data: AccountFormData) => {
    if (!primaryClient) {
      toast({
        title: "Error",
        description: "Please select a primary client",
        variant: "destructive",
      });
      return;
    }

    if (data.account_type === 'joint' && !secondaryClient) {
      toast({
        title: "Error",
        description: "Please select a secondary client for joint account",
        variant: "destructive",
      });
      return;
    }

    if (accountNumberError) {
      toast({
        title: "Invalid Account Number",
        description: accountNumberError,
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Create SB Account
      const accountData = {
        sb_account_number: data.sb_account_number,
        account_type: data.account_type,
        status: data.status,
        opening_balance: data.opening_balance ? parseFloat(data.opening_balance) : null,
        opened_at: data.opened_at || null,
        opened_by_agency: data.type === 'new',
        already_opened: data.type === 'existing',
        remarks: data.remarks || null,
      };

      const { data: accountResult, error: accountError } = await supabase
        .from('sb_accounts')
        .insert([accountData])
        .select()
        .single();

      if (accountError) throw accountError;

      // Create client-account relationships
      const clientAccountData = [];

      // Primary client
      clientAccountData.push({
        client_id: primaryClient.id,
        sb_account_id: accountResult.id,
        role: 'primary',
      });

      // Secondary client (for joint accounts)
      if (data.account_type === 'joint' && secondaryClient) {
        clientAccountData.push({
          client_id: secondaryClient.id,
          sb_account_id: accountResult.id,
          role: 'secondary',
        });
      }

      const { error: relationshipError } = await supabase
        .from('client_sb_accounts')
        .insert(clientAccountData);

      if (relationshipError) throw relationshipError;

      toast({
        title: "Success",
        description: "Account created successfully",
      });

      navigate('/accounts');
    } catch (error) {
      console.error('Error creating account:', error);
      toast({
        title: "Error",
        description: "Failed to create account",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => navigate('/accounts')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">Add New SB Account</h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Account Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="new">New</SelectItem>
                          <SelectItem value="existing">Existing</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="account_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select client type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="single">Single</SelectItem>
                          <SelectItem value="joint">Joint</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="sb_account_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SB Account Number *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter 11-digit account number" 
                        maxLength={11}
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, '');
                          field.onChange(value);
                          validateAccountNumber(value);
                        }}
                      />
                    </FormControl>
                    {isCheckingAccount && (
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                        Checking account number...
                      </div>
                    )}
                    {accountNumberError && (
                      <p className="text-sm text-red-500 mt-1">{accountNumberError}</p>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {typeValue === 'new' && (
                  <FormField
                    control={form.control}
                    name="opening_balance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Opening Balance</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter opening balance"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="closed">Closed</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="opened_at"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Opened At</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="remarks"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Remarks</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any remarks"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Client Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Client Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Primary Client *</label>
                <ClientSelector
                  selectedClient={primaryClient}
                  onClientSelect={setPrimaryClient}
                  required
                />
              </div>

              {accountTypeValue === 'joint' && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Secondary Client *</label>
                  <ClientSelector
                    selectedClient={secondaryClient}
                    onClientSelect={setSecondaryClient}
                    required
                  />
                </div>
              )}
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => navigate('/accounts')}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Creating...' : 'Create Account'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default AccountForm;
