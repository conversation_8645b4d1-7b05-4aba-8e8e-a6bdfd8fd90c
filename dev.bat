@echo off
echo 🔧 Starting Full Stack Application in Development Mode...

REM Check if .env file exists
if not exist .env (
    echo ❌ .env file not found. Please copy .env.example to .env and configure your environment variables.
    pause
    exit /b 1
)

echo 📋 Environment file found

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

echo 🐳 Docker is running

REM Start with development compose file if it exists
if exist docker\dev\docker-compose.dev.yml (
    echo 🔨 Building and starting services with development configuration...
    docker-compose -f docker-compose.yml -f docker\dev\docker-compose.dev.yml up --build -d
) else (
    echo 🔨 Building and starting services...
    docker-compose up --build -d
)

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 15 /nobreak >nul

REM Check service status
echo 📊 Service Status:
docker-compose ps

echo.
echo ✅ Development environment started successfully!
echo.
echo 🌐 Access your applications:
echo    Frontend:        http://localhost:3000
echo    Backend API:     http://localhost:3002
echo    Supabase Studio: http://localhost:3001
echo    Supabase API:    http://localhost:8000
if exist docker\dev\docker-compose.dev.yml (
    echo    Mail Server:     http://localhost:9000 (Inbucket)
    echo    Meta API:        http://localhost:5555
)
echo.
echo 📝 Development Commands:
echo    View logs:       docker-compose logs -f [service-name]
echo    Restart service: docker-compose restart [service-name]
echo    Shell access:    docker-compose exec [service-name] sh
echo    Stop:            docker-compose down
echo    Reset:           reset.bat
echo.
pause
