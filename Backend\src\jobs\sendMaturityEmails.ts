import { createClient } from '@supabase/supabase-js';
import { getTransporter } from '../utils/mailer';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Define alert types
type AlertStatus = 'pending' | 'sent' | 'failed';
type AlertChannel = 'email' | 'sms' | 'whatsapp';

export const sendMaturityEmails = async () => {
  // 1. Fetch settings
  const { data: settings, error: settingsError } = await supabase
    .from('notification_settings')
    .select('*')
    .single();

  if (settingsError || !settings?.email_enabled) {
    console.warn('Email notifications disabled or error fetching settings.');
    return;
  }

  // 2. Compute target maturity date
  const daysBefore = settings.alert_days_before || 3;
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() + daysBefore);
  const targetDateStr = targetDate.toISOString().split('T')[0];
  const fromEmail = settings.default_email_from 
  console.log('fromEmail', fromEmail);

  // 3. Get investments maturing on target date
  const { data: investments, error: invError } = await supabase
    .from('investments')
    .select('id, maturity_date, scheme_name, primary_applicant_cif_id, secondary_applicant_cif_id')
    .eq('maturity_date', targetDateStr)
    .eq('status', 'active');

  if (invError) {
    console.error('Failed to fetch investments:', invError.message);
    return;
  }

  // 4. Collect CIF IDs
  const cifIds = new Set<string>();
  investments?.forEach(inv => {
    if (inv.primary_applicant_cif_id) cifIds.add(inv.primary_applicant_cif_id);
    if (inv.secondary_applicant_cif_id) cifIds.add(inv.secondary_applicant_cif_id);
  });

  // 5. Get clients
  const { data: clients, error: clientError } = await supabase
    .from('clients')
    .select('cif_id, email, first_name, last_name')
    .in('cif_id', Array.from(cifIds));

  if (clientError) {
    console.error('Failed to fetch clients:', clientError.message);
    return;
  }

  const clientMap = new Map<string, { email: string; name: string }>();
  clients?.forEach(c => {
    if (c.email) {
      clientMap.set(c.cif_id, {
        email: c.email,
        name: `${c.first_name || ''} ${c.last_name || ''}`.trim(),
      });
    }
  });

  // 6. Send emails and record alerts
  let sent = 0;
  let errors: any[] = [];

  for (const inv of investments || []) {
    const recipients = [];

    if (clientMap.has(inv.primary_applicant_cif_id)) {
      recipients.push(clientMap.get(inv.primary_applicant_cif_id)!);
    }

    if (
      inv.secondary_applicant_cif_id &&
      inv.secondary_applicant_cif_id !== inv.primary_applicant_cif_id &&
      clientMap.has(inv.secondary_applicant_cif_id)
    ) {
      recipients.push(clientMap.get(inv.secondary_applicant_cif_id)!);
    }

    for (const recipient of recipients) {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; color: #333; max-width: 600px; margin: auto; border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; background-color: #fafafa;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #2c3e50;">📢 Investment Maturity Alert</h2>
          </div>
          <p>Dear <strong>${recipient.name || 'Investor'}</strong>,</p>
          <p>Your investment in <strong>${inv.scheme_name}</strong> is maturing on <strong>${inv.maturity_date}</strong>.</p>
          <p>👉 Please take action before the maturity date.</p>
          <p>If you have questions, contact our support team.</p>
          <div style="margin-top: 30px; text-align: center;">
            <p style="font-size: 14px; color: #888;">– Desai Investments</p>
          </div>
        </div>
      `;

      // Create alert record
      const alertMessage = `Your investment in ${inv.scheme_name} is maturing on ${inv.maturity_date}`;
      
      try {
        // Send email
        const transporter = await getTransporter();
        await transporter.sendMail({
          from: fromEmail,
          to: recipient.email,
          subject: `Your investment in ${inv.scheme_name} is maturing soon`,
          html: htmlContent,
        });

        // Get client ID from email
        const { data: clientData } = await supabase
          .from('clients')
          .select('id')
          .eq('email', recipient.email)
          .single();

        // Insert alert record with 'sent' status
        await supabase.from('alerts').insert({
          investment_id: inv.id,
          client_id: clientData?.id,
          alert_type: 'maturity',
          alert_date: new Date().toISOString().split('T')[0],
          message: alertMessage,
          status: 'sent' as AlertStatus,
          channel: 'email' as AlertChannel
        });

        sent++;
      } catch (err: any) {
        errors.push({ email: recipient.email, error: err.message });
        
        // Get client ID from email
        try {
          const { data: clientData } = await supabase
            .from('clients')
            .select('id')
            .eq('email', recipient.email)
            .single();

          // Insert alert record with 'failed' status
          await supabase.from('alerts').insert({
            investment_id: inv.id,
            client_id: clientData?.id,
            alert_type: 'maturity',
            alert_date: new Date().toISOString().split('T')[0],
            message: alertMessage,
            status: 'failed' as AlertStatus,
            channel: 'email' as AlertChannel
          });
        } catch (alertErr) {
          console.error('Failed to record alert failure:', alertErr);
        }
      }
    }
  }

  console.log(`✅ Emails sent: ${sent}, ❌ Errors: ${errors.length}`);
  if (errors.length) console.table(errors);
  
  // Log alerts summary
  const { count: alertCount } = await supabase
    .from('alerts')
    .select('*', { count: 'exact', head: true })
    .eq('alert_date', new Date().toISOString().split('T')[0]);
    
  console.log(`📊 Total alerts recorded today: ${alertCount || 0}`);
  
};
