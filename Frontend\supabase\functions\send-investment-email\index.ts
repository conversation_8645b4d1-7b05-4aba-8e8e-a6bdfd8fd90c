import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS"
};
const handler = async (req)=>{
  console.log('Email function called with method:', req.method);
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
  try {
    const requestBody = await req.json();
    const { investmentId, clientEmail, clientName } = requestBody;
    if (!investmentId || !clientEmail || !clientName) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Missing required fields: investmentId, clientEmail, or clientName'
      }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders
        }
      });
    }
    // Create Supabase client
    const supabase = createClient(Deno.env.get('SUPABASE_URL'), Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'));
    // Fetch investment details
    const { data: investment, error } = await supabase.from('investments').select(`*, client:clients!client_id (first_name, last_name, email, mobile_number)`).eq('id', investmentId).single();
    if (error || !investment) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Investment not found or error occurred'
      }), {
        status: 404,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders
        }
      });
    }
    // Get notification settings from database
    const { data: settings } = await supabase
      .from('notification_settings')
      .select('sendgrid_api_key, sendgrid_sender_email')
      .single();

    const sendGridApiKey = settings?.sendgrid_api_key;
    const fromEmail = settings?.sendgrid_sender_email;

    if (!sendGridApiKey || !fromEmail) {
      return new Response(JSON.stringify({
        success: false,
        message: 'SendGrid configuration missing in database settings'
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders
        }
      });
    }
    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Investment Details</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; }
          .email-wrapper { max-width: 650px; margin: 0 auto; background: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
          .header { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 40px 30px; text-align: center; position: relative; }
          .header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>'); }
          .header h1 { color: #ffffff; font-size: 32px; font-weight: 700; margin-bottom: 8px; position: relative; z-index: 1; }
          .header p { color: rgba(255,255,255,0.9); font-size: 16px; position: relative; z-index: 1; }
          .content { padding: 40px 30px; }
          .greeting { font-size: 18px; color: #1f2937; margin-bottom: 20px; line-height: 1.6; }
          .intro { font-size: 16px; color: #6b7280; margin-bottom: 30px; line-height: 1.6; }
          .investment-card { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 12px; padding: 30px; margin: 30px 0; border-left: 5px solid #2563eb; }
          .card-title { font-size: 20px; font-weight: 600; color: #1f2937; margin-bottom: 20px; display: flex; align-items: center; }
          .card-title::before { content: '💼'; margin-right: 10px; font-size: 24px; }
          .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
          .detail-item { background: #ffffff; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
          .detail-label { font-size: 12px; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px; }
          .detail-value { font-size: 16px; font-weight: 600; color: #1f2937; }
          .amount-highlight { color: #059669; font-size: 18px; }
          .status-badge { display: inline-block; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; }
          .status-active { background: #dcfce7; color: #166534; }
          .status-matured { background: #dbeafe; color: #1e40af; }
          .cta-section { text-align: center; margin: 30px 0; padding: 25px; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-radius: 12px; }
          .cta-text { font-size: 16px; color: #92400e; margin-bottom: 15px; font-weight: 500; }
          .contact-info { background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .contact-title { font-size: 16px; font-weight: 600; color: #1f2937; margin-bottom: 10px; }
          .footer { background: #1f2937; padding: 30px; text-align: center; }
          .footer-text { color: #9ca3af; font-size: 14px; line-height: 1.6; }
          .footer-brand { color: #ffffff; font-weight: 600; font-size: 16px; margin-bottom: 8px; }
          @media (max-width: 600px) {
            .details-grid { grid-template-columns: 1fr; }
            .content { padding: 30px 20px; }
            .header { padding: 30px 20px; }
          }
        </style>
      </head>
      <body>
        <div class="email-wrapper">
          <div class="header">
            <h1>Investment Confirmation</h1>
            <p>Your investment details and summary</p>
          </div>
          
          <div class="content">
            <div class="greeting">Dear ${clientName},</div>
            <div class="intro">Thank you for choosing us for your investment needs. We're pleased to confirm your investment details below:</div>
            
            <div class="investment-card">
              <div class="card-title">Investment Summary</div>
              <div class="details-grid">
                <div class="detail-item">
                  <div class="detail-label">Scheme Name</div>
                  <div class="detail-value">${investment.scheme_name}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">Scheme Code</div>
                  <div class="detail-value">${investment.scheme_code}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">Investment Amount</div>
                  <div class="detail-value amount-highlight">₹${investment.amount.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">Maturity Amount</div>
                  <div class="detail-value amount-highlight">₹${investment.maturity_amount.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">Investment Date</div>
                  <div class="detail-value">${new Date(investment.investment_date).toLocaleDateString('en-IN')}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">Maturity Date</div>
                  <div class="detail-value">${new Date(investment.maturity_date).toLocaleDateString('en-IN')}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">Interest Rate</div>
                  <div class="detail-value">${investment.interest_rate}% p.a.</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">Status</div>
                  <div class="detail-value">
                    <span class="status-badge status-${investment.status}">${investment.status.charAt(0).toUpperCase() + investment.status.slice(1)}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="cta-section">
              <div class="cta-text">🎉 Congratulations on your smart investment choice!</div>
              <div style="font-size: 14px; color: #92400e;">Your investment is now active and working towards your financial goals.</div>
            </div>
            
            <div class="contact-info">
              <div class="contact-title">Need Assistance?</div>
              <div style="color: #6b7280; font-size: 14px; line-height: 1.6;">
                Our investment advisors are here to help you with any questions or concerns.<br>
                Feel free to reach out to us anytime for support.
              </div>
            </div>
            
            <div style="margin-top: 30px; font-size: 16px; color: #1f2937; line-height: 1.6;">
              Best regards,<br>
              <strong style="color: #2563eb;">Investment Management Team</strong>
            </div>
          </div>
          
          <div class="footer">
            <div class="footer-brand">Investment Pro</div>
            <div class="footer-text">
              This is an automated email confirmation. Please do not reply to this email.<br>
              For support, please contact our customer service team.
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
    // Send via SendGrid API
    const sendGridPayload = {
      personalizations: [
        {
          to: [
            {
              email: clientEmail,
              name: clientName
            }
          ]
        }
      ],
      from: {
        email: fromEmail,
        name: "Investment Management Team"
      },
      subject: `🎯 Investment Confirmed - ${investment.scheme_name} | ₹${investment.amount.toLocaleString()}`,
      content: [
        {
          type: "text/html",
          value: emailContent
        }
      ]
    };
    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${sendGridApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(sendGridPayload)
    });
    if (!response.ok) {
      const errorText = await response.text();
      console.error("SendGrid error:", errorText);
      return new Response(JSON.stringify({
        success: false,
        message: "SendGrid API error",
        error: errorText
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders
        }
      });
    }
    return new Response(JSON.stringify({
      success: true,
      message: "Email sent successfully",
      recipient: clientEmail
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error("Function error:", error);
    return new Response(JSON.stringify({
      success: false,
      message: "Unexpected error",
      error: error.message
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders
      }
    });
  }
};
serve(handler);
