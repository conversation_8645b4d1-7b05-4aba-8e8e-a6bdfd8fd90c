@echo off
echo 🗑️  Resetting Full Stack Application...
echo ⚠️  WARNING: This will remove all data including database content!
set /p confirm=Are you sure you want to continue? (y/N): 

if /i "%confirm%"=="y" (
    echo 🛑 Stopping all services...
    docker-compose down -v --remove-orphans
    
    echo 🧹 Removing unused Docker resources...
    docker system prune -f
    
    echo 📁 Cleaning up volumes...
    docker volume prune -f
    
    echo ✅ Reset completed successfully!
    echo.
    echo 🚀 To start fresh: start.bat
) else (
    echo ❌ Reset cancelled.
)
pause
