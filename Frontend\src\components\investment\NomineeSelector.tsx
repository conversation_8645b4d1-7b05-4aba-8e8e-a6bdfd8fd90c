
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface Nominee {
  id: string;
  name: string;
  relation: string;
  birthdate?: string;
  document_url?: string;
}

interface NomineeSelectorProps {
  clientId: string;
  selectedNominee: Nominee | null;
  onNomineeSelect: (nominee: Nominee | null) => void;
}

const NomineeSelector: React.FC<NomineeSelectorProps> = ({
  clientId,
  selectedNominee,
  onNomineeSelect
}) => {
  const [nominees, setNominees] = useState<Nominee[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newNominee, setNewNominee] = useState({
    name: '',
    relation: '',
    birthdate: '',
    document: null as File | null
  });
  const [addingNominee, setAddingNominee] = useState(false);

  useEffect(() => {
    if (clientId) {
      fetchNominees();
    }
  }, [clientId]);

  const fetchNominees = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('nominees')
        .select('id, name, relation, birthdate, document_url')
        .eq('client_id', clientId)
        .order('name');

      if (error) throw error;
      setNominees(data || []);
    } catch (error) {
      console.error('Error fetching nominees:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNomineeSelect = (nomineeId: string) => {
    const nominee = nominees.find(n => n.id === nomineeId);
    onNomineeSelect(nominee || null);
  };

  const uploadDocument = async (file: File) => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}_nominee_doc.${fileExt}`;
    const filePath = `nominees/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('client-documents')
      .upload(filePath, file);

    if (uploadError) throw uploadError;
    return filePath;
  };

  const handleAddNominee = async () => {
    if (!newNominee.name.trim() || !newNominee.relation.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in both name and relation",
        variant: "destructive",
      });
      return;
    }

    setAddingNominee(true);
    try {
      let documentUrl = null;
      
      if (newNominee.document) {
        documentUrl = await uploadDocument(newNominee.document);
      }

      const { data, error } = await supabase
        .from('nominees')
        .insert([{
          client_id: clientId,
          name: newNominee.name.trim(),
          relation: newNominee.relation.trim(),
          birthdate: newNominee.birthdate || null,
          document_url: documentUrl
        }])
        .select()
        .single();

      if (error) throw error;

      // Add to local state
      setNominees(prev => [...prev, data]);
      
      // Select the newly added nominee
      onNomineeSelect(data);
      
      // Reset form and close dialog
      setNewNominee({ name: '', relation: '', birthdate: '', document: null });
      setDialogOpen(false);
      
      toast({
        title: "Success",
        description: "Nominee added successfully",
      });
    } catch (error) {
      console.error('Error adding nominee:', error);
      toast({
        title: "Error",
        description: "Failed to add nominee",
        variant: "destructive",
      });
    } finally {
      setAddingNominee(false);
    }
  };

  return (
    <div className="flex gap-2">
      <div className="flex-1">
        <Select 
          value={selectedNominee?.id || ''} 
          onValueChange={handleNomineeSelect}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a nominee" />
          </SelectTrigger>
          <SelectContent>
            {nominees.map((nominee) => (
              <SelectItem key={nominee.id} value={nominee.id}>
                {nominee.name} ({nominee.relation})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogTrigger asChild>
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="whitespace-nowrap"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Nominee
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Nominee</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="nominee-name">Name *</Label>
              <Input
                id="nominee-name"
                placeholder="Enter nominee name"
                value={newNominee.name}
                onChange={(e) => setNewNominee(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="nominee-relation">Relation *</Label>
              <Input
                id="nominee-relation"
                placeholder="Enter relation (e.g., Son, Daughter, Spouse)"
                value={newNominee.relation}
                onChange={(e) => setNewNominee(prev => ({ ...prev, relation: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="nominee-birthdate">Birthdate (Optional)</Label>
              <Input
                id="nominee-birthdate"
                type="date"
                value={newNominee.birthdate}
                onChange={(e) => setNewNominee(prev => ({ ...prev, birthdate: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="nominee-document">Upload Document (Optional)</Label>
              <Input
                id="nominee-document"
                type="file"
                accept="image/*,.pdf,.doc,.docx"
                onChange={(e) => setNewNominee(prev => ({ ...prev, document: e.target.files?.[0] || null }))}
              />
            </div>
          </div>
          <div className="flex justify-end gap-3">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                setDialogOpen(false);
                setNewNominee({ name: '', relation: '', birthdate: '', document: null });
              }}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleAddNominee}
              disabled={addingNominee}
            >
              {addingNominee ? 'Adding...' : 'Add Nominee'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NomineeSelector;
