import { createClient } from '@supabase/supabase-js';
import axios from 'axios';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

type AlertStatus = 'pending' | 'sent' | 'failed';
type AlertChannel = 'email' | 'sms' | 'whatsapp';

const formatPhoneNumber = (phone: string): string => {
  const digits = phone.replace(/\D/g, '');
  return digits.startsWith('91') ? `+${digits}` : `+91${digits}`;
};

export const sendMaturitySMS = async () => {
  console.log('🔔 Running maturity SMS alerts...');

  try {
    // 1. Fetch settings
    const { data: settings, error: settingsError } = await supabase
      .from('notification_settings')
      .select('*')
      .single();

    if (settingsError || !settings?.sms_enabled) {
      console.warn('SMS notifications disabled or error fetching settings.');
      return;
    }

    // 2. Compute target maturity date
    const daysBefore = settings.alert_days_before || 3;
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + daysBefore);
    const targetDateStr = targetDate.toISOString().split('T')[0];

    // 3. Get investments maturing on target date
    const { data: investments, error: invError } = await supabase
      .from('investments')
      .select(`
        id,
        maturity_date,
        scheme_name,
        primary_applicant_cif_id,
        secondary_applicant_cif_id
      `)
      .eq('maturity_date', targetDateStr)
      .eq('status', 'active');

    if (invError) {
      console.error('Failed to fetch investments:', invError.message);
      return;
    }

    // 4. Collect CIF IDs
    const cifIds = new Set<string>();
    investments?.forEach(inv => {
      if (inv.primary_applicant_cif_id) cifIds.add(inv.primary_applicant_cif_id);
      if (inv.secondary_applicant_cif_id) cifIds.add(inv.secondary_applicant_cif_id);
    });

    // 5. Get clients with their contact details
    const { data: clients, error: clientError } = await supabase
      .from('clients')
      .select(`
        id,
        cif_id,
        first_name,
        last_name,
        mobile_number,
        contact_person2
      `)
      .in('cif_id', Array.from(cifIds));

    if (clientError) {
      console.error('Failed to fetch clients:', clientError.message);
      return;
    }

    // Create a map of client details
    const clientMap = new Map();
    clients?.forEach(client => {
      clientMap.set(client.cif_id, {
        id: client.id,
        name: `${client.first_name || ''} ${client.last_name || ''}`.trim(),
        mobile: client.mobile_number,
        contact2: {
          mobile: client.contact_person2
        }
      });
    });

    let sent = 0;
    let errors: any[] = [];

    // 6. Send SMS for each investment
    for (const inv of investments || []) {
      const recipients: { name: string; mobile: string }[] = [];

      // Add primary applicant
      if (clientMap.has(inv.primary_applicant_cif_id)) {
        const client = clientMap.get(inv.primary_applicant_cif_id);
        if (client.mobile) {
          recipients.push({ name: client.name, mobile: client.mobile });
        }
        // Add contact person 2 if exists
        if (client.contact2?.mobile) {
          recipients.push({ 
            name: client.contact2.name || 'Contact Person 2',
            mobile: client.contact2.mobile 
          });
        }
      }

      // Add secondary applicant
      if (inv.secondary_applicant_cif_id && 
          inv.secondary_applicant_cif_id !== inv.primary_applicant_cif_id &&
          clientMap.has(inv.secondary_applicant_cif_id)) {
        const client = clientMap.get(inv.secondary_applicant_cif_id);
        if (client.mobile) {
          recipients.push({ name: client.name, mobile: client.mobile });
        }
      }

      // Send SMS to each recipient
      for (const recipient of recipients) {
        try {
          const smsMessage = `Dear ${recipient.name}, Your investment in ${inv.scheme_name} is maturing on ${inv.maturity_date}. Please take action before maturity date. - Desai Investments`;

          const response = await axios({
            method: 'post',
            url: `https://api.twilio.com/2010-04-01/Accounts/${settings.twilio_account_sid}/Messages.json`,
            headers: {
              'Authorization': `Basic ${Buffer.from(`${settings.twilio_account_sid}:${settings.twilio_auth_token}`).toString('base64')}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: new URLSearchParams({
              'To': formatPhoneNumber(recipient.mobile),
              'From': settings.twilio_phone_number,
              'Body': smsMessage
            })
          });

          console.log(`✅ SMS sent to ${recipient.name} (${recipient.mobile})`);
          sent++;

          // Record success
          await supabase.from('alerts').insert({
            investment_id: inv.id,
            client_id: clientMap.get(inv.primary_applicant_cif_id).id,
            alert_type: 'maturity',
            alert_date: new Date().toISOString().split('T')[0],
            message: smsMessage,
            status: 'sent' as AlertStatus,
            channel: 'sms' as AlertChannel
          });

        } catch (err: any) {
          console.error(`Failed to send SMS to ${recipient.mobile}:`, err.message);
          errors.push({ mobile: recipient.mobile, error: err.message });

          // Record failure
          await supabase.from('alerts').insert({
            investment_id: inv.id,
            client_id: clientMap.get(inv.primary_applicant_cif_id).id,
            alert_type: 'maturity',
            alert_date: new Date().toISOString().split('T')[0],
            message: `Failed to send maturity SMS: ${err.message}`,
            status: 'failed' as AlertStatus,
            channel: 'sms' as AlertChannel
          });
        }
      }
    }

    console.log(`✅ SMS sent: ${sent}, ❌ Errors: ${errors.length}`);
    if (errors.length) console.table(errors);

  } catch (err: any) {
    console.error('🔥 Error in maturity SMS alerts:', err.message);
  }
};