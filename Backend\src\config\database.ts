import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

dotenv.config();
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is not defined');
}

const sequelize = new Sequelize(process.env.DATABASE_URL, {
  dialect: 'postgres',
  logging: false, // Disable logging for production
  define: {
    timestamps: false, // Disable timestamps globally
    freezeTableName: true // Prevent Sequelize from pluralizing table names
  }
});

export default sequelize;