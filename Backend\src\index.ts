import express from 'express';
import sequelize from './config/database';
import userRoutes from './routes/userRoutes';
import dotenv from 'dotenv';
import { QueryTypes } from 'sequelize';
import cron from 'node-cron';
import { sendMaturityEmails } from './jobs/sendMaturityEmails';
import { sendMonthlyAdminReport } from './jobs/sendMonthlyAdminReport';
import { sendMaturitySMS } from './jobs/sendMaturitySMS';
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());

// Routes
app.use('/api/users', userRoutes);

// Test database connection
const testDbConnection = async (): Promise<void> => {
  try {
    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');
    
    // Verify we can query the users table
    const result = await sequelize.query('SELECT COUNT(*) FROM users', {
      type: QueryTypes.SELECT
    });
    console.log('Users table accessible:', result);
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
};
// Schedule maturity email alerts to run every minute (for testing)
cron.schedule('* * * * *', async () => {
  console.log(`⏰ Running maturity email cron job at ${new Date().toLocaleString()}`);
  try {
    await sendMaturityEmails();
  } catch (error) {
    console.error('Error in maturity email cron job:', error);
  }
});

// Schedule monthly admin report to run at 9:00 AM on the 1st day of each month
cron.schedule('* * * * *', async () => {
  console.log(`⏰ Running monthly admin report cron job at ${new Date().toLocaleString()}`);
  try {
    await sendMonthlyAdminReport();
  } catch (error) {
    console.error('Error in monthly admin report cron job:', error);
  }
});

cron.schedule('* * * * *', async () => {  // Runs at 9 AM daily
  await sendMaturitySMS();
}); 


// // For testing the monthly admin report (runs every hour)
// cron.schedule('0 * * * *', async () => {
//   console.log(`⏰ Testing monthly admin report at ${new Date().toLocaleString()}`);
//   try {
//     await sendMonthlyAdminReport();
//   } catch (error) {
//     console.error('Error in test monthly admin report:', error);
//   }
// });

// Start server
app.listen(PORT, async () => {
  await testDbConnection();
  console.log(`Server is running on port ${PORT}`);
});