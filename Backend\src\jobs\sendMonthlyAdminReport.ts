import { createClient } from '@supabase/supabase-js';
import { getTransporter } from '../utils/mailer';
import axios from 'axios';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Define alert types
type AlertStatus = 'pending' | 'sent' | 'failed';
type AlertChannel = 'email' | 'sms' | 'whatsapp';

export const sendMonthlyAdminReport = async (): Promise<void> => {
  console.log('🔔 Running monthly admin report...');

  try {
    // 1. Get notification settings
    const { data: settings, error: settingsError } = await supabase
      .from('notification_settings')
      .select('*')
      .single();

    if (settingsError) {
      console.error('Failed to fetch notification settings:', settingsError.message);
      return;
    }

    // Check if it's the 1st day of the month
    const today = new Date();
    const isFirstDayOfMonth = today.getDate() === 1;
    
    // For testing, comment out this check
    // if (!isFirstDayOfMonth) {
    //   console.log('Not the first day of the month. Skipping admin report.');
    //   return;
    // }

    // 2. Get current month's maturing investments
    const currentMonth = today.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = today.getFullYear();
    const firstDayOfMonth = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`;
    const lastDayOfMonth = new Date(currentYear, currentMonth, 0).getDate();
    const lastDateOfMonth = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${lastDayOfMonth}`;

    // 3. Fetch investments maturing this month
    const { data: investments, error: invError } = await supabase
      .from('investments')
      .select(`
        id, 
        scheme_name, 
        scheme_code,
        amount, 
        maturity_amount, 
        maturity_date, 
        interest_rate,
        client_id,
        second_applicant_id
      `)
      .gte('maturity_date', firstDayOfMonth)
      .lte('maturity_date', lastDateOfMonth)
      .eq('status', 'active');

    if (invError) {
      console.error('Failed to fetch investments:', invError.message);
      return;
    }

    if (!investments || investments.length === 0) {
      console.log('No investments maturing this month.');
      return;
    }

    // 4. Get client details for these investments
    const clientIds = new Set<string>();
    investments.forEach(inv => {
      if (inv.client_id) clientIds.add(inv.client_id);
      if (inv.second_applicant_id) clientIds.add(inv.second_applicant_id);
    });

    const { data: clients, error: clientError } = await supabase
      .from('clients')
      .select('id, first_name, last_name, email, mobile_number')
      .in('id', Array.from(clientIds));

    if (clientError) {
      console.error('Failed to fetch clients:', clientError.message);
      return;
    }

    // Create a map of client IDs to client details
    const clientMap = new Map();
    clients?.forEach(client => {
      clientMap.set(client.id, {
        name: `${client.first_name || ''} ${client.last_name || ''}`.trim(),
        email: client.email,
        mobile: client.mobile_number
      });
    });

    // 5. Generate report data
    const totalInvestments = investments.length;
    const totalAmount = investments.reduce((sum, inv) => sum + Number(inv.amount), 0);
    const totalMaturityAmount = investments.reduce((sum, inv) => sum + Number(inv.maturity_amount), 0);
    const totalProfit = totalMaturityAmount - totalAmount;

    // 6. Generate HTML for email
    const monthName = today.toLocaleString('default', { month: 'long' });
   const investmentRows = investments.map(inv => {
  const primaryClient = clientMap.get(inv.client_id);
  const secondaryClient = inv.second_applicant_id ? clientMap.get(inv.second_applicant_id) : null;

  return `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${inv.scheme_name} (${inv.scheme_code || 'N/A'})</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${primaryClient?.name || 'Unknown'}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${secondaryClient?.name || 'N/A'}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">₹${Number(inv.amount).toLocaleString('en-IN')}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">₹${Number(inv.maturity_amount).toLocaleString('en-IN')}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${inv.maturity_date}</td>
    </tr>
  `;
}).join('');


    const htmlContent = `
  <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; padding: 0; background-color: #ffffff; border: 1px solid #e0e0e0; border-radius: 6px; overflow: hidden;">
    
    <!-- Header -->
    <div style="background-color: #2c3e50; color: #ffffff; padding: 20px; text-align: center;">
      <h2 style="margin: 0;">Monthly Investment Maturity Report</h2>
      <p style="margin: 5px 0 0;">${monthName} ${currentYear}</p>
    </div>

    <!-- Summary -->
    <div style="padding: 20px;">
      <h3 style="color: #2c3e50; margin-top: 0;">Summary</h3>
      <table style="width: 100%; border-collapse: collapse; text-align: left; margin-bottom: 20px;">
        <tr>
          <td style="padding: 8px 0;">Total Investments:</td>
          <td style="padding: 8px 0;"><strong>${totalInvestments}</strong></td>
        </tr>
        <tr>
          <td style="padding: 8px 0;">Total Invested Amount:</td>
          <td style="padding: 8px 0;">₹${totalAmount.toLocaleString('en-IN')}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0;">Total Profit:</td>
          <td style="padding: 8px 0;">₹${totalProfit.toLocaleString('en-IN')}</td>
        </tr>
      </table>

      <!-- Table of Maturing Investments -->
      <h3 style="color: #2c3e50;">Maturing Investments</h3>
      <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
          <thead>
            <tr style="background-color: #f2f2f2;">
              <th style="padding: 10px; border-bottom: 1px solid #ddd;">Scheme</th>
              <th style="padding: 10px; border-bottom: 1px solid #ddd;">Primary</th>
              <th style="padding: 10px; border-bottom: 1px solid #ddd;">Secondary</th>
              <th style="padding: 10px; border-bottom: 1px solid #ddd;">Amount</th>
              <th style="padding: 10px; border-bottom: 1px solid #ddd;">Maturity</th>
              <th style="padding: 10px; border-bottom: 1px solid #ddd;">Date</th>
            </tr>
          </thead>
          <tbody>
            ${investmentRows}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Footer -->
    <div style="background-color: #f9f9f9; color: #666666; padding: 15px; text-align: center; font-size: 12px;">
      <p style="margin: 0;">Automated report sent on ${today.toISOString().split('T')[0]}</p>
    </div>
  </div>
`;

    // 7. Send email to admin
    if (settings.email_enabled && settings.admin_email) {
      try {
        const transporter = await getTransporter();
        await transporter.sendMail({
          from: settings.default_email_from || process.env.EMAIL_USER,
          to: settings.admin_email,
          subject: `Monthly Investment Maturity Report - ${monthName} ${currentYear}`,
          html: htmlContent,
        });
        
        console.log(`✅ Admin email report sent to ${settings.admin_email}`);
        
        // Record alert
        await supabase.from('alerts').insert({
          alert_type: 'admin_monthly_report',
          alert_date: today.toISOString().split('T')[0],
          message: `Monthly investment maturity report for ${monthName} ${currentYear} sent to admin`,
          status: 'sent' as AlertStatus,
          channel: 'email' as AlertChannel
        });
      } catch (err: any) {
        console.error('Failed to send admin email:', err.message);
        
        // Record failure
        await supabase.from('alerts').insert({
          alert_type: 'admin_monthly_report',
          alert_date: today.toISOString().split('T')[0],
          message: `Failed to send monthly report email: ${err.message}`,
          status: 'failed' as AlertStatus,
          channel: 'email' as AlertChannel
        });
      }
    }

    // 8. Send SMS to admin if enabled and phone number is available
    if (settings.sms_enabled && settings.admin_phone && settings.twilio_account_sid && settings.twilio_auth_token) {
      try {
        // Prepare SMS message
        // const smsMessage = `Monthly Investment Report (${monthName} ${currentYear}): ${totalInvestments} investments maturing this month with total value of ₹${totalAmount.toLocaleString('en-IN')} and profit of ₹${totalProfit.toLocaleString('en-IN')}.`;

        const smsMessage = `Monthly Investment Report`
        
        // Send SMS using Twilio
        const twilioEndpoint = `https://api.twilio.com/2010-04-01/Accounts/${settings.twilio_account_sid}/Messages.json`;
        const twilioAuth = Buffer.from(`${settings.twilio_account_sid}:${settings.twilio_auth_token}`).toString('base64');
        
        const response = await axios({
          method: 'post',
          url: twilioEndpoint,
          headers: {
            'Authorization': `Basic ${twilioAuth}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          data: new URLSearchParams({
            'To': `+91${settings.admin_phone}`,
            'From': settings.twilio_phone_number,
            'Body': smsMessage
          })
        });
        
        console.log(`✅ Admin SMS report sent to ${settings.admin_phone}`);
        
        // Record alert
        await supabase.from('alerts').insert({
          alert_type: 'admin_monthly_report',
          alert_date: today.toISOString().split('T')[0],
          message: `Monthly investment maturity report SMS for ${monthName} ${currentYear} sent to admin`,
          status: 'sent' as AlertStatus,
          channel: 'sms' as AlertChannel
        });
      } catch (err: any) {
        console.error('Failed to send admin SMS:', err.message);
        
        // Record failure
        await supabase.from('alerts').insert({
          alert_type: 'admin_monthly_report',
          alert_date: today.toISOString().split('T')[0],
          message: `Failed to send monthly report SMS: ${err.message}`,
          status: 'failed' as AlertStatus,
          channel: 'sms' as AlertChannel
        });
      }
    }
    
  } catch (err: any) {
    console.error('🔥 Error in monthly admin report:', err.message || err);
  }
};