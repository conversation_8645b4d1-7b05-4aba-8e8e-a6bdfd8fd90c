{"name": "new-folder-(4)", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@supabase/supabase-js": "^2.52.0", "@types/nodemailer": "^6.4.17", "axios": "^1.10.0", "dotenv": "^17.2.0", "express": "^4.18.2", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.19.8", "ts-node": "^10.9.1", "tsx": "^4.20.3", "typescript": "^5.8.3"}}