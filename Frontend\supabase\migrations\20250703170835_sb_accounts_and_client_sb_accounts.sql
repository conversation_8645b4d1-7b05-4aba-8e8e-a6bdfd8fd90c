
-- Remove sb_account_number column from clients table
ALTER TABLE public.clients DROP COLUMN sb_account_number;

-- Create sb_accounts table
CREATE TABLE public.sb_accounts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sb_account_number varchar(11) NOT NULL UNIQUE,
  account_type varchar(10) NOT NULL CHECK (account_type IN ('single', 'joint')),
  already_opened boolean NOT NULL DEFAULT false,
  opened_by_agency boolean NOT NULL DEFAULT true,
  opening_balance numeric(12, 2),
  status varchar(20) NOT NULL DEFAULT 'active',
  opened_at timestamptz NULL,
  closed_at timestamptz NULL,
  is_deleted boolean NOT NULL DEFAULT false,
  remarks text,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Create client_sb_accounts junction table
CREATE TABLE public.client_sb_accounts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id uuid NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  sb_account_id uuid NOT NULL REFERENCES sb_accounts(id) ON DELETE CASCADE,
  role varchar(20) NOT NULL DEFAULT 'joint', -- 'primary', 'joint', 'guardian'
  added_at timestamptz NOT NULL DEFAULT now(),
  CONSTRAINT unique_client_sb_account UNIQUE (client_id, sb_account_id)
);

-- Add payment_mode and sb_account_id columns to transactions table
ALTER TABLE public.transactions 
ADD COLUMN payment_mode varchar(20) CHECK (payment_mode IN ('sb_account', 'cheque', 'ecs')),
ADD COLUMN sb_account_id uuid REFERENCES sb_accounts(id);

-- Enable RLS on new tables
ALTER TABLE public.sb_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_sb_accounts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for sb_accounts
CREATE POLICY "Authenticated users can access all data" ON public.sb_accounts FOR ALL USING (auth.role() = 'authenticated');

-- Create RLS policies for client_sb_accounts
CREATE POLICY "Authenticated users can access all data" ON public.client_sb_accounts FOR ALL USING (auth.role() = 'authenticated');
