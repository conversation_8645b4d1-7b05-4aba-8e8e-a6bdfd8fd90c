#!/bin/bash

# Full Stack Application Startup Script
# This script starts the entire application stack including Supabase, Frontend, and Backend

echo "🚀 Starting Full Stack Application..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure your environment variables."
    exit 1
fi

# Load environment variables
source .env

echo "📋 Environment loaded"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "🐳 Docker is running"

# Build and start all services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "✅ Application started successfully!"
echo ""
echo "🌐 Access your applications:"
echo "   Frontend:        http://localhost:3000"
echo "   Backend API:     http://localhost:3002"
echo "   Supabase Studio: http://localhost:3001"
echo "   Supabase API:    http://localhost:8000"
echo ""
echo "📝 To view logs: docker-compose logs -f [service-name]"
echo "🛑 To stop: docker-compose down"
echo "🗑️  To reset: docker-compose down -v --remove-orphans"
