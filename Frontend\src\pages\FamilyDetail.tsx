import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ArrowLeft, Edit, Plus, Eye, FileText, Users, TrendingUp, Calendar } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { formatDisplayDate } from '@/utils/dateFormat';
import { exportToPDF } from '@/utils/pdfExport';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string | null;
  mobile_number: string;
  address: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  village: string | null;
  cif_id: string | null;
  pan_card_number: string | null;
  aadhar_number: string | null;
  created_at: string;
}

interface Investment {
  id: string;
  amount: number;
  investment_date: string;
  maturity_date: string;
  maturity_amount: number;
  status: string;
  remark: string | null;
  scheme: {
    name: string;
    scheme_code: string;
  };
}

interface Transaction {
  id: string;
  amount: number;
  transaction_date: string;
  amount_type: string;
  payment_mode: string;
  reference_number: string | null;
  investment: {
    scheme: {
      name: string;
    };
  };
}

const FamilyDetail: React.FC = () => {
  const { clientUnitHash } = useParams<{ clientUnitHash: string }>();
  const navigate = useNavigate();
  const [primaryClient, setPrimaryClient] = useState<Client | null>(null);
  const [secondaryClient, setSecondaryClient] = useState<Client | null>(null);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalInvestments: 0,
    activeInvestments: 0,
    totalAmount: 0,
    activeAmount: 0,
    maturedAmount: 0
  });

  useEffect(() => {
    if (clientUnitHash) {
      fetchFamilyDetails();
    }
  }, [clientUnitHash]);

  const fetchFamilyDetails = async () => {
    try {
      setLoading(true);

      // Fetch investments for this client unit
      const { data: investmentData, error: investmentError } = await supabase
        .from('investments')
        .select(`
          *,
          scheme:schemes(name, scheme_code),
          client:clients!client_id(
            id, first_name, last_name, email, mobile_number,
            address, city, state, pincode, village, cif_id,
            pan_card_number, aadhar_number, created_at
          ),
          secondary_applicant:clients!second_applicant_id(
            id, first_name, last_name, email, mobile_number,
            address, city, state, pincode, village, cif_id,
            pan_card_number, aadhar_number, created_at
          )
        `)
        .eq('client_unit_hash', clientUnitHash)
        .eq('is_active', true)
        .order('investment_date', { ascending: false });

      if (investmentError) throw investmentError;

      setInvestments(investmentData || []);

      // Set primary and secondary clients from the first investment
      if (investmentData && investmentData.length > 0) {
        const firstInvestment = investmentData[0];
        setPrimaryClient(firstInvestment.client);
        setSecondaryClient(firstInvestment.secondary_applicant);
      }

      // Fetch transactions for all investments in this family unit
      const investmentIds = (investmentData || []).map(inv => inv.id);
      if (investmentIds.length > 0) {
        const { data: transactionData, error: transactionError } = await supabase
          .from('transactions')
          .select(`
            *,
            investment:investments!inner(
              scheme:schemes(name)
            )
          `)
          .in('investment_id', investmentIds)
          .order('transaction_date', { ascending: false });

        if (transactionError) throw transactionError;
        setTransactions(transactionData || []);
      }

      // Calculate stats
      const totalInvestments = investmentData?.length || 0;
      const activeInvestments = investmentData?.filter(inv => inv.status === 'active').length || 0;
      const totalAmount = investmentData?.reduce((sum, inv) => sum + Number(inv.amount || 0), 0) || 0;
      const activeAmount = investmentData?.filter(inv => inv.status === 'active').reduce((sum, inv) => sum + Number(inv.amount || 0), 0) || 0;
      const maturedInvestments = investmentData?.filter(inv => inv.status === 'matured' || inv.status === 'withdrawn') || [];
      const maturedAmount = maturedInvestments.reduce((sum, inv) => sum + Number(inv.maturity_amount || 0), 0) || 0;

      console.log('All investments:', investmentData);
      console.log('Matured/withdrawn investments:', maturedInvestments);
      console.log('Matured amount calculated:', maturedAmount);

      setStats({
        totalInvestments,
        activeInvestments,
        totalAmount,
        activeAmount,
        maturedAmount
      });

    } catch (error) {
      console.error('Error fetching family details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch family details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return '₹' + amount.toLocaleString('en-IN', { maximumFractionDigits: 2 });
  };

  const generateFamilyReport = async () => {
    if (!primaryClient) return;

    try {
      const familyName = secondaryClient
        ? `${primaryClient.first_name}${primaryClient.last_name ? ` ${primaryClient.last_name}` : ''} & ${secondaryClient.first_name}${secondaryClient.last_name ? ` ${secondaryClient.last_name}` : ''}`
        : `${primaryClient.first_name}${primaryClient.last_name ? ` ${primaryClient.last_name}` : ''}`;

      const familyType = secondaryClient ? 'Joint Family' : 'Individual Family';

      // Prepare investment details
      const investmentDetails = investments.map((investment, index) => `
        <tr>
          <td>${index + 1}</td>
          <td>
            <div><strong>${investment.scheme.name}</strong></div>
            <div style="font-size: 12px; color: #666;">${investment.scheme.scheme_code}</div>
          </td>
          <td style="text-align: right; color: #059669;">${formatCurrency(investment.amount)}</td>
          <td>${formatDisplayDate(investment.investment_date)}</td>
          <td>${formatDisplayDate(investment.maturity_date)}</td>
          <td style="text-align: right; color: #2563eb;">${formatCurrency(investment.maturity_amount)}</td>
          <td style="text-align: center;">
            <span class="status-badge status-${investment.status}">${investment.status.charAt(0).toUpperCase() + investment.status.slice(1)}</span>
          </td>
          <td>${investment.remark || 'No remarks'}</td>
        </tr>
      `).join('');

      // Prepare transaction details
      const transactionDetails = transactions.slice(0, 20).map((transaction, index) => `
        <tr>
          <td>${index + 1}</td>
          <td>${formatDisplayDate(transaction.transaction_date)}</td>
          <td>${transaction.investment.scheme.name}</td>
          <td>
            <span class="amount-type-badge amount-type-${transaction.amount_type}">${transaction.amount_type.replace(/_/g, ' ')}</span>
          </td>
          <td style="text-align: right; color: ${['investment', 'reinvestment'].includes(transaction.amount_type) ? '#059669' : '#dc2626'};">
            ${['investment', 'reinvestment'].includes(transaction.amount_type) ? '+' : '-'}${formatCurrency(transaction.amount)}
          </td>
          <td>${transaction.payment_mode || 'N/A'}</td>
          <td>${transaction.reference_number || 'N/A'}</td>
        </tr>
      `).join('');

      const content = `
        <div class="family-header">
          <h2>${familyName}</h2>
          <p class="family-type">${familyType}</p>
        </div>

        <div class="stats-grid">
          <div class="stat-card">
            <h4>Total Investments</h4>
            <p class="stat-value">${stats.totalInvestments}</p>
          </div>
          <div class="stat-card">
            <h4>Active Investments</h4>
            <p class="stat-value">${stats.activeInvestments}</p>
          </div>
          <div class="stat-card">
            <h4>Total Amount</h4>
            <p class="stat-value">${formatCurrency(stats.totalAmount)}</p>
          </div>
          <div class="stat-card">
            <h4>Active Amount</h4>
            <p class="stat-value">${formatCurrency(stats.activeAmount)}</p>
          </div>
          <div class="stat-card">
            <h4>Matured Amount</h4>
            <p class="stat-value">${formatCurrency(stats.maturedAmount)}</p>
          </div>
        </div>

        <div class="client-details">
          <h3>Primary Client Details</h3>
          <div class="client-info">
            <div class="info-row">
              <span class="label">Full Name:</span>
              <span class="value">${primaryClient.first_name}${primaryClient.last_name ? ` ${primaryClient.last_name}` : ''}</span>
            </div>
            <div class="info-row">
              <span class="label">Mobile Number:</span>
              <span class="value">${primaryClient.mobile_number}</span>
            </div>
            <div class="info-row">
              <span class="label">Email:</span>
              <span class="value">${primaryClient.email || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">CIF ID:</span>
              <span class="value">${primaryClient.cif_id || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Village:</span>
              <span class="value">${primaryClient.village || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">PAN Card:</span>
              <span class="value">${primaryClient.pan_card_number || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Aadhar Number:</span>
              <span class="value">${primaryClient.aadhar_number || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Address:</span>
              <span class="value">${primaryClient.address || 'N/A'}${primaryClient.city && primaryClient.state ? `, ${primaryClient.city}, ${primaryClient.state} - ${primaryClient.pincode}` : ''}</span>
            </div>
          </div>

          ${secondaryClient ? `
            <h3>Secondary Client Details</h3>
            <div class="client-info">
              <div class="info-row">
                <span class="label">Full Name:</span>
                <span class="value">${secondaryClient.first_name}${secondaryClient.last_name ? ` ${secondaryClient.last_name}` : ''}</span>
              </div>
              <div class="info-row">
                <span class="label">Mobile Number:</span>
                <span class="value">${secondaryClient.mobile_number}</span>
              </div>
              <div class="info-row">
                <span class="label">Email:</span>
                <span class="value">${secondaryClient.email || 'N/A'}</span>
              </div>
              <div class="info-row">
                <span class="label">CIF ID:</span>
                <span class="value">${secondaryClient.cif_id || 'N/A'}</span>
              </div>
              <div class="info-row">
                <span class="label">Village:</span>
                <span class="value">${secondaryClient.village || 'N/A'}</span>
              </div>
              <div class="info-row">
                <span class="label">PAN Card:</span>
                <span class="value">${secondaryClient.pan_card_number || 'N/A'}</span>
              </div>
              <div class="info-row">
                <span class="label">Aadhar Number:</span>
                <span class="value">${secondaryClient.aadhar_number || 'N/A'}</span>
              </div>
              <div class="info-row">
                <span class="label">Address:</span>
                <span class="value">${secondaryClient.address || 'N/A'}${secondaryClient.city && secondaryClient.state ? `, ${secondaryClient.city}, ${secondaryClient.state} - ${secondaryClient.pincode}` : ''}</span>
              </div>
            </div>
          ` : ''}
        </div>

        ${investments.length > 0 ? `
          <h3>Family Investments (${investments.length})</h3>
          <table class="report-table">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Scheme</th>
                <th>Amount</th>
                <th>Investment Date</th>
                <th>Maturity Date</th>
                <th>Maturity Amount</th>
                <th>Status</th>
                <th>Remarks</th>
              </tr>
            </thead>
            <tbody>
              ${investmentDetails}
            </tbody>
          </table>
        ` : ''}

        ${transactions.length > 0 ? `
          <h3>Recent Transactions (${Math.min(transactions.length, 20)})</h3>
          <table class="report-table">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Date</th>
                <th>Scheme</th>
                <th>Type</th>
                <th>Amount</th>
                <th>Payment Mode</th>
                <th>Reference</th>
              </tr>
            </thead>
            <tbody>
              ${transactionDetails}
            </tbody>
          </table>
        ` : ''}

        <style>
          .family-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
          }
          .family-header h2 {
            margin: 0 0 8px 0;
            color: #1e293b;
            font-size: 24px;
          }
          .family-type {
            margin: 0;
            color: #64748b;
            font-size: 16px;
          }
          .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
          }
          .stat-card {
            background: #fff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
          }
          .stat-card h4 {
            margin: 0 0 8px 0;
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .stat-value {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
          }
          .client-details {
            margin-bottom: 30px;
          }
          .client-details h3 {
            margin: 20px 0 15px 0;
            color: #1e293b;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 8px;
          }
          .client-info {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
          }
          .info-row {
            display: flex;
            margin-bottom: 12px;
            align-items: flex-start;
          }
          .info-row:last-child {
            margin-bottom: 0;
          }
          .label {
            font-weight: 600;
            color: #475569;
            min-width: 140px;
            flex-shrink: 0;
          }
          .value {
            color: #1e293b;
            flex: 1;
          }
          .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            text-transform: capitalize;
          }
          .status-active {
            background: #dcfce7;
            color: #166534;
          }
          .status-matured {
            background: #fef3c7;
            color: #92400e;
          }
          .status-withdrawn {
            background: #fee2e2;
            color: #991b1b;
          }
          .amount-type-badge {
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 500;
            text-transform: capitalize;
          }
          .amount-type-investment, .amount-type-reinvestment {
            background: #dcfce7;
            color: #166534;
          }
          .amount-type-withdrawal, .amount-type-penalty {
            background: #fee2e2;
            color: #991b1b;
          }
          .amount-type-interest_payout {
            background: #dbeafe;
            color: #1d4ed8;
          }
          .amount-type-commission {
            background: #f3e8ff;
            color: #7c3aed;
          }
        </style>
      `;

      const filterSummary = [
        { label: 'Family Name', value: familyName, applied: true },
        { label: 'Family Type', value: familyType, applied: true },
        { label: 'Total Investments', value: stats.totalInvestments.toString(), applied: true },
        { label: 'Active Investments', value: stats.activeInvestments.toString(), applied: true },
        { label: 'Total Amount', value: formatCurrency(stats.totalAmount), applied: true }
      ];

      await exportToPDF({
        title: `Family Report - ${familyName}`,
        content,
        filterSummary
      });

    } catch (error) {
      console.error('Error generating family report:', error);
      toast({
        title: "Error",
        description: "Failed to generate family report",
        variant: "destructive",
      });
    }
  };

  const handleAddInvestment = () => {
    if (!primaryClient) return;

    // Navigate to investment form with pre-selected clients
    navigate('/investments/new', {
      state: {
        prefilledData: {
          client_id: primaryClient.id,
          second_applicant_id: secondaryClient?.id || null,
          remark: `Investment for ${secondaryClient ? 'joint family' : 'individual'} - ${primaryClient.first_name}${primaryClient.last_name ? ` ${primaryClient.last_name}` : ''}${secondaryClient ? ` & ${secondaryClient.first_name}${secondaryClient.last_name ? ` ${secondaryClient.last_name}` : ''}` : ''}`
        }
      }
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, label: 'Active' },
      matured: { variant: 'secondary' as const, label: 'Matured' },
      withdrawn: { variant: 'outline' as const, label: 'Withdrawn' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getAmountTypeColor = (type: string) => {
    switch (type) {
      case 'investment': return 'bg-green-100 text-green-800 border-green-200';
      case 'reinvestment': return 'bg-green-100 text-green-800 border-green-200';
      case 'interest_payout': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'withdrawal': return 'bg-red-100 text-red-800 border-red-200';
      case 'penalty': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'commission': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading family details...</div>;
  }

  if (!primaryClient) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Family not found</h3>
        <p className="text-gray-500 mb-4">The requested family unit could not be found.</p>
        <Button onClick={() => navigate('/family')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Families
        </Button>
      </div>
    );
  }

  const familyType = secondaryClient ? 'Joint Family' : 'Individual Family';
  const familyName = secondaryClient
    ? `${primaryClient.first_name}${primaryClient.last_name ? ` ${primaryClient.last_name}` : ''} & ${secondaryClient.first_name}${secondaryClient.last_name ? ` ${secondaryClient.last_name}` : ''}`
    : `${primaryClient.first_name}${primaryClient.last_name ? ` ${primaryClient.last_name}` : ''}`;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate('/family')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="min-w-0 flex-1">
            <h1 className="text-xl md:text-2xl lg:text-3xl font-bold truncate">{familyName}</h1>
            <p className="text-gray-600">{familyType}</p>
          </div>
        </div>
        <div className="flex gap-2 w-full md:w-auto">
          <Button variant="outline" onClick={generateFamilyReport} className="flex-1 md:flex-initial text-xs sm:text-sm whitespace-nowrap">
            <FileText className="h-4 w-4 mr-1 sm:mr-2 flex-shrink-0" />
            <span className="truncate">Generate Report</span>
          </Button>
          <Button onClick={handleAddInvestment} className="flex-1 md:flex-initial text-xs sm:text-sm whitespace-nowrap">
            <Plus className="h-4 w-4 mr-1 sm:mr-2 flex-shrink-0" />
            <span className="truncate">Add Investment</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Investments</p>
                <p className="text-2xl font-bold">{stats.totalInvestments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active</p>
                <p className="text-2xl font-bold">{stats.activeInvestments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Total Amount</p>
              <p className="text-xl font-bold text-blue-600">{formatCurrency(stats.totalAmount)}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Active Amount</p>
              <p className="text-xl font-bold text-green-600">{formatCurrency(stats.activeAmount)}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Matured Amount</p>
              <p className="text-xl font-bold text-orange-600">{formatCurrency(stats.maturedAmount)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Client Details Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Primary Client */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Primary Client
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Full Name</label>
                <p className="font-medium">{primaryClient.first_name}{primaryClient.last_name ? ` ${primaryClient.last_name}` : ''}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Mobile Number</label>
                <p className="font-medium">{primaryClient.mobile_number}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="font-medium">{primaryClient.email || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">CIF ID</label>
                <p className="font-medium text-blue-600">{primaryClient.cif_id || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Village</label>
                <p className="font-medium">{primaryClient.village || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">PAN Card</label>
                <p className="font-medium">{primaryClient.pan_card_number || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Aadhar Number</label>
                <p className="font-medium">{primaryClient.aadhar_number || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Joined Date</label>
                <p className="font-medium">{formatDisplayDate(primaryClient.created_at)}</p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Address</label>
              <p className="font-medium">
                {primaryClient.address || 'N/A'}
                {primaryClient.city && primaryClient.state && (
                  <span className="text-gray-500">
                    <br />{primaryClient.city}, {primaryClient.state} - {primaryClient.pincode}
                  </span>
                )}
              </p>
            </div>
            <div className="pt-2 flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`/clients/${primaryClient.id}`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`/clients/${primaryClient.id}/edit`)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Client
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Secondary Client */}
        {secondaryClient ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Secondary Client
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="font-medium">{secondaryClient.first_name}{secondaryClient.last_name ? ` ${secondaryClient.last_name}` : ''}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Mobile Number</label>
                  <p className="font-medium">{secondaryClient.mobile_number}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="font-medium">{secondaryClient.email || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">CIF ID</label>
                  <p className="font-medium text-blue-600">{secondaryClient.cif_id || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Village</label>
                  <p className="font-medium">{secondaryClient.village || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">PAN Card</label>
                  <p className="font-medium">{secondaryClient.pan_card_number || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Aadhar Number</label>
                  <p className="font-medium">{secondaryClient.aadhar_number || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Joined Date</label>
                  <p className="font-medium">{formatDisplayDate(secondaryClient.created_at)}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Address</label>
                <p className="font-medium">
                  {secondaryClient.address || 'N/A'}
                  {secondaryClient.city && secondaryClient.state && (
                    <span className="text-gray-500">
                      <br />{secondaryClient.city}, {secondaryClient.state} - {secondaryClient.pincode}
                    </span>
                  )}
                </p>
              </div>
              <div className="pt-2 flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/clients/${secondaryClient.id}`)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/clients/${secondaryClient.id}/edit`)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Client
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-gray-400" />
                Secondary Client
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No secondary client for this family unit</p>
                <p className="text-sm text-gray-400 mt-2">This is an individual family</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Investments Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Family Investments ({investments.length})
            </div>
            <Button onClick={handleAddInvestment}>
              <Plus className="h-4 w-4 mr-2" />
              Add Investment
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {investments.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Scheme</TableHead>
                    <TableHead className="min-w-[120px] text-right font-semibold">Amount</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Investment Date</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Maturity Date</TableHead>
                    <TableHead className="min-w-[120px] text-right font-semibold">Maturity Amount</TableHead>
                    <TableHead className="min-w-[100px] text-center font-semibold">Status</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">Remarks</TableHead>
                    <TableHead className="min-w-[100px] text-right sticky right-0 bg-gray-50 font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {investments.map((investment, index) => (
                    <TableRow key={investment.id} className="hover:bg-gray-50">
                      <TableCell className="w-16 text-center font-medium">
                        <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                      </TableCell>
                      <TableCell className="min-w-[150px]">
                        <div>
                          <div className="font-medium text-gray-900">{investment.scheme.name}</div>
                          <div className="text-sm text-gray-500">{investment.scheme.scheme_code}</div>
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right">
                        <div className="font-medium text-green-600">
                          {formatCurrency(investment.amount)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {formatDisplayDate(investment.investment_date)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {formatDisplayDate(investment.maturity_date)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right">
                        <div className="font-medium text-blue-600">
                          {formatCurrency(investment.maturity_amount)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px] text-center">
                        {getStatusBadge(investment.status)}
                      </TableCell>
                      <TableCell className="min-w-[200px]">
                        <div className="text-sm text-gray-600 truncate max-w-[200px]">
                          {investment.remark || 'No remarks'}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px] text-right sticky right-0 bg-white">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/investments/${investment.id}`)}
                            className="h-8 w-8 p-0 hover:bg-blue-50"
                            title="View Investment"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/investments/${investment.id}/edit`)}
                            className="h-8 w-8 p-0 hover:bg-blue-50"
                            title="Edit Investment"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No investments found</h3>
              <p className="text-gray-500 mb-4">This family unit has no investments yet.</p>
              <Button onClick={handleAddInvestment}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Investment
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Transactions Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Transactions ({transactions.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {transactions.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Scheme</TableHead>
                    <TableHead className="min-w-[120px] text-right font-semibold">Amount</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Transaction Date</TableHead>
                    <TableHead className="min-w-[100px] font-semibold">Type</TableHead>
                    <TableHead className="min-w-[120px] font-semibold">Payment Mode</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Reference</TableHead>
                    <TableHead className="min-w-[100px] text-right sticky right-0 bg-gray-50 font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction, index) => (
                    <TableRow key={transaction.id} className="hover:bg-gray-50">
                      <TableCell className="w-16 text-center font-medium">
                        <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                      </TableCell>
                      <TableCell className="min-w-[150px]">
                        <div className="font-medium text-gray-900">
                          {transaction.investment.scheme.name}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px] text-right">
                        <div className={`font-medium ${['investment', 'reinvestment'].includes(transaction.amount_type)
                          ? 'text-green-600'
                          : 'text-red-600'
                          }`}>
                          {['investment', 'reinvestment'].includes(transaction.amount_type) ? '+' : '-'}{formatCurrency(transaction.amount)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {formatDisplayDate(transaction.transaction_date)}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px]">
                        <Badge variant="outline" className={`capitalize ${getAmountTypeColor(transaction.amount_type)}`}>
                          {transaction.amount_type.replace(/_/g, ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell className="min-w-[120px]">
                        <div className="text-sm font-medium">
                          {transaction.payment_mode || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[150px]">
                        <div className="text-sm text-gray-600 truncate max-w-[150px]">
                          {transaction.reference_number || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[100px] text-right sticky right-0 bg-white">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/transactions/${transaction.id}`)}
                            className="h-8 w-8 p-0 hover:bg-blue-50"
                            title="View Transaction"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
              <p className="text-gray-500 mb-4">No transactions have been recorded for this family unit yet.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FamilyDetail;
