import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

export const DataTableControls = ({ 
  searchTerm, 
  onSearchChange,
  onRefresh,
  placeholder = "Search records..."
}) => {
  return (
    <div className="flex flex-col md:flex-row gap-4 py-4">
      <Input
        placeholder={placeholder}
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className="md:max-w-sm"
      />
      <div className="flex gap-2">
        <Button variant="outline" onClick={onRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>
    </div>
  );
};