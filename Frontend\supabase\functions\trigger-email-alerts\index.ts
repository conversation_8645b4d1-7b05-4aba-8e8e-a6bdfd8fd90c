
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS"
};

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    // Call the existing maturity alert function
    const { data: emailResult, error: emailError } = await supabase.rpc('send_maturity_alerts_email');
    
    // Call the SMS alert function if enabled
    const { data: smsResult, error: smsError } = await supabase.rpc('send_maturity_sms_alerts');

    const results = {
      email: emailResult || { success: false, message: emailError?.message || 'Email alerts failed' },
      sms: smsResult || { success: false, message: smsError?.message || 'SMS alerts failed' }
    };

    console.log('Alert results:', results);

    return new Response(JSON.stringify({
      success: true,
      message: 'Alert process completed',
      results
    }), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders }
    });

  } catch (error: any) {
    console.error('Alert trigger error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json", ...corsHeaders }
    });
  }
};

serve(handler);
