import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Plus, Search, Eye, Edit, Trash2, Users, TrendingUp, Calendar, User, ArrowUpDown, X, FileText } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { EnhancedDateFilter, DateRange, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { formatDisplayDate } from '@/utils/dateFormat';
import { exportToPDF } from '@/utils/pdfExport';

interface ClientUnit {
  client_unit_hash: string;
  primary_client_name: string;
  secondary_client_name: string | null;
  primary_client_mobile: string;
  primary_client_email: string | null;
  unit_type: string;
  total_investments: number;
  total_investment_amount: string;
  active_investments: number;
  active_amount: string;
  created_at?: string | null;
}

const Family: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [clientUnits, setClientUnits] = useState<ClientUnit[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [filter, setFilter] = useState(searchParams.get('filter') || 'all');
  const [dateFilter, setDateFilter] = useState('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(parseInt(searchParams.get('limit') || '10'));
  const [totalCount, setTotalCount] = useState(0);
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'primary_client_name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>((searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc');
  const [stats, setStats] = useState({
    total: 0,
    joint: 0,
    individual: 0,
    withInvestments: 0
  });

  // Handle explicit search action
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setCurrentPage(1); // Reset to first page on search
  };

  useEffect(() => {
    fetchClientUnits();
  }, [searchTerm, filter, dateFilter, customDateRange, currentPage, itemsPerPage, sortBy, sortOrder]);

  useEffect(() => {
    fetchStats();
  }, []);

  // Update URL parameters when sorting changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    if (sortBy !== 'primary_client_name') params.set('sortBy', sortBy);
    else params.delete('sortBy');
    if (sortOrder !== 'asc') params.set('sortOrder', sortOrder);
    else params.delete('sortOrder');
    setSearchParams(params);
  }, [sortBy, sortOrder, searchParams, setSearchParams]);

  const fetchStats = async () => {
    try {
      const { data, error } = await supabase.rpc('get_client_units_summary');
      if (error) throw error;

      // Type assertion to ensure data is treated as an array of ClientUnit
      const units = (data as unknown as ClientUnit[]) || [];
      const stats = {
        total: units.length,
        joint: units.filter((unit: ClientUnit) => unit.unit_type === 'Joint').length,
        individual: units.filter((unit: ClientUnit) => unit.unit_type === 'Individual').length,
        withInvestments: units.filter((unit: ClientUnit) => unit.total_investments > 0).length
      };
      setStats(stats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchClientUnits = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('get_client_units_summary');

      if (error) throw error;

      // Type assertion to ensure data is treated as an array of ClientUnit
      let unitsData = (data as unknown as ClientUnit[]) || [];

      // Apply search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase().trim();
        unitsData = unitsData.filter((unit: ClientUnit) =>
          (unit.primary_client_name && unit.primary_client_name.toLowerCase().includes(searchLower)) ||
          (unit.secondary_client_name && unit.secondary_client_name.toLowerCase().includes(searchLower)) ||
          (unit.primary_client_mobile && unit.primary_client_mobile.toLowerCase().includes(searchLower)) ||
          (unit.primary_client_email && unit.primary_client_email.toLowerCase().includes(searchLower))
        );
      }

      // Apply filter
      if (filter === 'joint') {
        unitsData = unitsData.filter((unit: ClientUnit) => unit.unit_type === 'Joint');
      } else if (filter === 'individual') {
        unitsData = unitsData.filter((unit: ClientUnit) => unit.unit_type === 'Individual');
      } else if (filter === 'with-investments') {
        unitsData = unitsData.filter((unit: ClientUnit) => unit.total_investments > 0);
      } else if (filter === 'no-investments') {
        unitsData = unitsData.filter((unit: ClientUnit) => unit.total_investments === 0);
      }

      // Apply sorting
      unitsData.sort((a: ClientUnit, b: ClientUnit) => {
        let aValue = a[sortBy as keyof ClientUnit];
        let bValue = b[sortBy as keyof ClientUnit];

        if (typeof aValue === 'string') aValue = aValue.toLowerCase();
        if (typeof bValue === 'string') bValue = bValue.toLowerCase();

        if (sortOrder === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      setTotalCount(unitsData.length);

      // Apply pagination
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedData = unitsData.slice(startIndex, endIndex);

      setClientUnits(paginatedData);
    } catch (error) {
      console.error('Error fetching client units:', error);
      toast({
        title: "Error",
        description: "Failed to fetch family data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  const SortableHeader = ({ column, children, className = "" }: { column: string; children: React.ReactNode; className?: string }) => (
    <TableHead
      className={`cursor-pointer hover:bg-gray-100 select-none font-semibold ${className}`}
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-2">
        {children}
        <ArrowUpDown className="h-4 w-4" />
        {sortBy === column && (
          <span className="text-xs text-blue-600">
            {sortOrder === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </TableHead>
  );

  // Pagination logic
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page

    // Update URL parameters
    const params = new URLSearchParams(searchParams);
    params.set('limit', newItemsPerPage.toString());
    params.set('page', '1');
    setSearchParams(params);
  };

  const generateFamilyReport = async () => {
    const familyWithDetails = clientUnits.map(unit => ({
      ...unit,
      unit_type: unit.unit_type || 'Individual',
      primary_client_email: unit.primary_client_email || 'N/A'
    }));

    const filterSummary = [
      { label: 'Search Term', value: searchTerm || 'All', applied: !!searchTerm },
      { label: 'Filter Type', value: filter === 'all' ? 'All Families' : filter.replace(/-/g, ' '), applied: filter !== 'all' },
      { label: 'Date Filter', value: dateFilter === 'all' ? 'All Dates' : dateFilter, applied: dateFilter !== 'all' },
      { label: 'Total Records', value: totalCount.toString(), applied: true }
    ];

    const tableContent = `
      <table class="report-table">
        <thead>
          <tr>
            <th>S.No</th>
            <th>Family</th>
            <th>Type</th>
            <th>Contact</th>
            <th>Total Investments</th>
            <th>Active Investments</th>
            <th>Total Amount</th>
            <th>Active Amount</th>
          </tr>
        </thead>
        <tbody>
          ${familyWithDetails.map((unit, index) => {
      const familyName = unit.secondary_client_name
        ? `${unit.primary_client_name} & ${unit.secondary_client_name}`
        : unit.primary_client_name;
      const contactInfo = `${unit.primary_client_mobile}${unit.primary_client_email !== 'N/A' ? ` | ${unit.primary_client_email}` : ''}`;

      return `
            <tr>
              <td>${(currentPage - 1) * itemsPerPage + index + 1}</td>
              <td><strong>${familyName}</strong></td>
              <td>${unit.unit_type}</td>
              <td>${contactInfo}</td>
              <td>${unit.total_investments}</td>
              <td>${unit.active_investments}</td>
              <td>₹${parseFloat(unit.total_investment_amount).toLocaleString()}</td>
              <td>₹${parseFloat(unit.active_amount).toLocaleString()}</td>
            </tr>
          `;
    }).join('')}
        </tbody>
      </table>
    `;

    await exportToPDF({
      title: 'Family Report',
      content: tableContent,
      filterSummary
    });
  };

  const formatCurrency = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return '₹' + numAmount.toLocaleString('en-IN', { maximumFractionDigits: 2 });
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading family data...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Family</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={generateFamilyReport}>
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button onClick={() => navigate('/clients/new')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Client
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4">
        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'all' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => setFilter('all')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-blue-100 rounded-lg">
                <Users className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Total Families</p>
                <p className="text-lg md:text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'joint' ? 'ring-2 ring-green-500' : ''}`}
          onClick={() => setFilter('joint')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Joint Families</p>
                <p className="text-lg md:text-2xl font-bold">{stats.joint}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'individual' ? 'ring-2 ring-orange-500' : ''}`}
          onClick={() => setFilter('individual')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-orange-100 rounded-lg">
                <User className="h-4 w-4 md:h-5 md:w-5 text-orange-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Individual Families</p>
                <p className="text-lg md:text-2xl font-bold">{stats.individual}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'with-investments' ? 'ring-2 ring-purple-500' : ''}`}
          onClick={() => setFilter('with-investments')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-purple-100 rounded-lg">
                <Calendar className="h-4 w-4 md:h-5 md:w-5 text-purple-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">With Investments</p>
                <p className="text-lg md:text-2xl font-bold">{stats.withInvestments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <div className="relative w-full">
              <Input
                placeholder="Search by name, email, or mobile..."
                value={searchInput}
                onChange={(e) => {
                  setSearchInput(e.target.value);
                  // Auto-search when clearing all text (empty search)
                  if (e.target.value === '') {
                    setSearchTerm('');
                    setCurrentPage(1);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSearch();
                  }
                }}
                className="pl-10 pr-20"
                autoComplete="off"
              />
              {searchInput && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-16 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => {
                    setSearchInput('');
                    setSearchTerm('');
                    setCurrentPage(1);
                  }}
                  title="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 px-3"
                onClick={handleSearch}
              >
                Search
              </Button>
            </div>
          </div>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Families</SelectItem>
              <SelectItem value="joint">Joint Families</SelectItem>
              <SelectItem value="individual">Individual Families</SelectItem>
              <SelectItem value="with-investments">With Investments</SelectItem>
              <SelectItem value="no-investments">No Investments</SelectItem>
            </SelectContent>
          </Select>
          <div className="w-full lg:w-auto">
            <EnhancedDateFilter
              value={dateFilter}
              onChange={setDateFilter}
              customRange={customDateRange}
              onCustomRangeChange={setCustomDateRange}
            />
          </div>
        </div>
      </div>

      {/* Family List Table */}
      <Card>
        <CardHeader>
          <CardTitle>Family Units ({totalCount} total)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {clientUnits.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                      <SortableHeader column="primary_client_name" className="min-w-[200px]">Primary Client</SortableHeader>
                      <TableHead className="font-semibold hidden md:table-cell min-w-[180px]">Secondary Client</TableHead>
                      <SortableHeader column="unit_type" className="min-w-[100px]">Type</SortableHeader>
                      <SortableHeader column="total_investments" className="min-w-[100px] text-center">Investments</SortableHeader>
                      <SortableHeader column="total_investment_amount" className="min-w-[120px] text-right">Total Amount</SortableHeader>
                      <TableHead className="min-w-[120px] text-right sticky right-0 bg-gray-50 font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clientUnits.map((unit, index) => (
                      <TableRow key={unit.client_unit_hash} className="hover:bg-gray-50">
                        <TableCell className="w-16 text-center font-medium">
                          <span className="text-sm font-bold text-blue-600">{(currentPage - 1) * itemsPerPage + index + 1}</span>
                        </TableCell>
                        <TableCell className="min-w-[200px]">
                          <div className="space-y-0.5">
                            <div className="flex items-center gap-1">
                              <span className="font-medium text-gray-900">
                                {unit.primary_client_name}
                              </span>
                              <span className="text-xs px-1 py-0.5 bg-blue-50 text-blue-700 rounded">
                                Primary
                              </span>
                            </div>
                            <div className="flex flex-wrap items-center gap-x-2 text-xs text-gray-500">
                              <span>📱 {unit.primary_client_mobile}</span>
                              {unit.primary_client_email && (
                                <span>✉️ {unit.primary_client_email}</span>
                              )}
                            </div>

                            {/* Show secondary client on mobile - compact version */}
                            {unit.secondary_client_name && (
                              <div className="md:hidden mt-1 pt-1 border-t border-gray-100">
                                <div className="flex items-center gap-1">
                                  <span className="text-xs text-gray-500">+</span>
                                  <span className="text-xs font-medium">
                                    {unit.secondary_client_name}
                                  </span>
                                  <span className="text-xs px-1 py-0.5 bg-gray-50 text-gray-500 rounded">
                                    Secondary
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell min-w-[180px]">
                          {unit.secondary_client_name ? (
                            <div className="space-y-0.5">
                              <div className="flex items-center gap-1">
                                <span className="font-medium text-gray-900">
                                  {unit.secondary_client_name}
                                </span>
                                <span className="text-xs px-1 py-0.5 bg-blue-50 text-blue-700 rounded">
                                  Secondary
                                </span>
                              </div>
                              <div className="flex flex-wrap items-center gap-x-2 text-xs text-gray-500">
                                <span>📱 Contact via Primary</span>
                              </div>
                            </div>
                          ) : (
                            <div className="text-xs text-gray-400 italic">
                              No secondary client
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="min-w-[100px]">
                          <Badge variant={unit.unit_type === 'Joint' ? 'outline' : 'secondary'}>
                            {unit.unit_type}
                          </Badge>
                        </TableCell>
                        <TableCell className="min-w-[100px] text-center">
                          <div className="font-medium">{unit.total_investments}</div>
                          <div className="text-xs text-gray-500">{unit.active_investments} active</div>
                        </TableCell>
                        <TableCell className="min-w-[120px] text-right">
                          <div className="font-medium text-green-600">{formatCurrency(unit.total_investment_amount)}</div>
                          <div className="text-xs text-gray-500">{formatCurrency(unit.active_amount)} active</div>
                        </TableCell>
                        <TableCell className="min-w-[120px] text-right sticky right-0 bg-white">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/family/${unit.client_unit_hash}`)}
                              className="h-8 w-8 p-0 hover:bg-blue-50"
                              title="View Family Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Enhanced Responsive Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t bg-gray-50">
                  {/* Mobile Pagination */}
                  <div className="flex flex-col space-y-4 sm:hidden">
                    <div className="text-sm text-gray-600 text-center">
                      Page {currentPage} of {totalPages} ({totalCount} total results)
                    </div>
                    <div className="flex justify-between items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        <span className="text-xs">‹</span>
                        Previous
                      </Button>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Page</span>
                        <select
                          value={currentPage}
                          onChange={(e) => handlePageChange(Number(e.target.value))}
                          className="px-3 py-1 border border-gray-300 rounded-md text-sm bg-white min-w-[60px] text-center"
                        >
                          {Array.from({ length: totalPages }, (_, i) => (
                            <option key={i + 1} value={i + 1}>{i + 1}</option>
                          ))}
                        </select>
                        <span className="text-sm text-gray-500">of {totalPages}</span>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        Next
                        <span className="text-xs">›</span>
                      </Button>
                    </div>
                  </div>

                  {/* Desktop Pagination */}
                  <div className="hidden sm:flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="text-sm text-gray-600 order-2 lg:order-1">
                      Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalCount)}</span> of <span className="font-medium">{totalCount}</span> results
                    </div>

                    <div className="flex items-center justify-center lg:justify-end gap-2 order-1 lg:order-2">
                      {/* Previous Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="text-sm">‹</span>
                        <span className="hidden md:inline">Previous</span>
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                          let page: number;
                          if (totalPages <= 5) {
                            page = i + 1;
                          } else if (currentPage <= 3) {
                            page = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            page = totalPages - 4 + i;
                          } else {
                            page = currentPage - 2 + i;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className={`w-10 h-10 p-0 ${currentPage === page
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'hover:bg-gray-100'
                                }`}
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>

                      {/* Next Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="hidden md:inline">Next</span>
                        <span className="text-sm">›</span>
                      </Button>
                    </div>
                  </div>

                  {/* Items per page selector for larger screens */}
                  <div className="hidden xl:flex items-center justify-center mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>Show</span>
                      <select
                        value={itemsPerPage}
                        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                        className="px-2 py-1 border border-gray-300 rounded text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                      <span>entries per page</span>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No family units found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || filter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding clients and creating investments'
                }
              </p>
              {!searchTerm && filter === 'all' && (
                <Button onClick={() => navigate('/clients/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Client
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Family;