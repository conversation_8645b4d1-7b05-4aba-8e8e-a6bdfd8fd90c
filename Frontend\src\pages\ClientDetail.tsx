import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>H<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Plus, Eye, FileText } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  village: string;
  cif_id: string;
  pan_card_number: string;
  aadhar_number: string;
  contact_person2: string;
  client_photo_url: string;
  aadhar_photo_url: string;
  pan_photo_url: string;
  created_at: string;
}

interface Investment {
  id: string;
  amount: number;
  investment_date: string;
  maturity_date: string;
  maturity_amount: number;
  status: string;
  client_id: string;
  second_applicant_id: string | null;
  applicant_type: 'primary' | 'secondary';
  scheme: {
    name: string;
    scheme_code: string;
  };
}

interface Nominee {
  id: string;
  name: string;
  relation: string;
  birthdate: string;
  document_url?: string;
}

interface Transaction {
  id: string;
  amount: number;
  transaction_date: string;
  amount_type: string;
  reference_number: string;
  investment: {
    scheme: {
      name: string;
    };
  };
}

interface SbAccount {
  id: string;
  sb_account_number: string;
  account_type: string;
  status: string;
  opened_at: string;
  client_role: string;
}

const ClientDetail: React.FC = () => {
  // Utility function to capitalize first letter of each word
  const capitalizeFirstLetter = (text: string): string => {
    if (!text) return text;
    return text
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [client, setClient] = useState<Client | null>(null);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [nominees, setNominees] = useState<Nominee[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [sbAccounts, setSbAccounts] = useState<SbAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddNominee, setShowAddNominee] = useState(false);
  const [newNominee, setNewNominee] = useState({ name: '', relation: '', birthdate: '', document: null as File | null });
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchClientDetails();
    }
  }, [id]);

  const fetchClientDetails = async () => {
    try {
      // Fetch client details
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('*')
        .eq('id', id)
        .eq('is_deleted', false)
        .single();

      if (clientError) throw clientError;
      setClient(clientData);

      // Fetch investments where client is primary applicant
      const { data: primaryInvestments, error: primaryError } = await supabase
        .from('investments')
        .select(`
          *,
          scheme:schemes(name, scheme_code)
        `)
        .eq('client_id', id)
        .eq('is_active', true);

      if (primaryError) throw primaryError;

      // Fetch investments where client is secondary applicant
      const { data: secondaryInvestments, error: secondaryError } = await supabase
        .from('investments')
        .select(`
          *,
          scheme:schemes(name, scheme_code)
        `)
        .eq('second_applicant_id', id)
        .eq('is_active', true);

      if (secondaryError) throw secondaryError;

      // Combine and mark investments with applicant type
      const allInvestments = [
        ...(primaryInvestments || []).map(inv => ({ ...inv, applicant_type: 'primary' as const })),
        ...(secondaryInvestments || []).map(inv => ({ ...inv, applicant_type: 'secondary' as const }))
      ];

      setInvestments(allInvestments);

      // Fetch nominees
      const { data: nomineeData, error: nomineeError } = await supabase
        .from('nominees')
        .select('*')
        .eq('client_id', id);

      if (nomineeError) throw nomineeError;
      setNominees(nomineeData || []);

      // Fetch recent transactions
      const { data: transactionData, error: transactionError } = await supabase
        .from('transactions')
        .select(`
          *,
          investment:investments!inner(
            scheme:schemes(name)
          )
        `)
        .eq('investment.client_id', id)
        .order('transaction_date', { ascending: false })
        .limit(10);

      if (transactionError) throw transactionError;
      setTransactions(transactionData || []);

      // Fetch SB accounts for this specific client
      const { data: sbAccountData, error: sbAccountError } = await supabase
        .from('client_sb_accounts')
        .select(`
          role,
          sb_accounts (
            id,
            sb_account_number,
            account_type,
            status,
            opened_at,
            is_deleted
          )
        `)
        .eq('client_id', id)
        .eq('sb_accounts.is_deleted', false);

      if (sbAccountError) throw sbAccountError;
      // Transform the data to match the expected format
      const transformedAccounts = (sbAccountData || []).map(item => ({
        ...item.sb_accounts,
        client_role: item.role
      }));
      setSbAccounts(transformedAccounts || []);

    } catch (error) {
      console.error('Error fetching client details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch client details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const uploadFile = async (file: File, path: string) => {
    const { data, error } = await supabase.storage
      .from('client-documents')
      .upload(path, file);

    if (error) throw error;
    return data.path;
  };

  const handleAddNominee = async () => {
    if (!newNominee.name || !newNominee.relation) {
      toast({
        title: "Error",
        description: "Name and relation are required",
        variant: "destructive",
      });
      return;
    }

    try {
      let documentUrl = null;

      // Upload document if provided
      if (newNominee.document) {
        const path = `nominees/${Date.now()}_${newNominee.document.name}`;
        documentUrl = await uploadFile(newNominee.document, path);
      }

      const { error } = await supabase
        .from('nominees')
        .insert([{
          name: newNominee.name,
          relation: newNominee.relation,
          birthdate: newNominee.birthdate || null,
          client_id: id,
          document_url: documentUrl,
        }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Nominee added successfully",
      });

      setNewNominee({ name: '', relation: '', birthdate: '', document: null });
      setShowAddNominee(false);
      fetchClientDetails();
    } catch (error) {
      console.error('Error adding nominee:', error);
      toast({
        title: "Error",
        description: "Failed to add nominee",
        variant: "destructive",
      });
    }
  };

  const getImageUrl = (path: string) => {
    if (!path) return null;
    const { data } = supabase.storage.from('client-documents').getPublicUrl(path);
    return data.publicUrl;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading client details...</div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Client not found</div>
      </div>
    );
  }

  const activeInvestments = investments.filter(inv => inv.status === 'active');
  const inactiveInvestments = investments.filter(inv => inv.status !== 'active');

  return (
    <div className="space-y-6 max-w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/clients')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back to Clients</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">
            {client.first_name} {client.last_name}
          </h1>
        </div>
        <Button
          onClick={() => navigate(`/clients/${id}/edit`)}
          className="w-full sm:w-auto"
          size="sm"
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Client
        </Button>
      </div>

      {/* Client Details */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Client Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p className="text-base">{client.first_name} {client.last_name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p className="text-base">{client.email || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Mobile Number</label>
              <p className="text-base">{client.mobile_number}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">CIF ID</label>
              <p className="text-base">{client.cif_id}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">PAN Card Number</label>
              <p className="text-base">{client.pan_card_number}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Aadhar Number</label>
              <p className="text-base">{client.aadhar_number}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Address</label>
              <p className="text-base">{client.address}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">City, State</label>
              <p className="text-base">{client.city}, {client.state}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Pincode</label>
              <p className="text-base">{client.pincode}</p>
            </div>
            {client.village && (
              <div>
                <label className="text-sm font-medium text-gray-500">Village</label>
                <p className="text-base">{client.village}</p>
              </div>
            )}
            {client.contact_person2 && (
              <div>
                <label className="text-sm font-medium text-gray-500">Secondary Contact</label>
                <p className="text-base">{client.contact_person2}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {client.client_photo_url && (
              <div className="text-center">
                <label className="text-sm font-medium text-gray-500">Profile Photo</label>
                <div className="mt-2">
                  <img
                    src={getImageUrl(client.client_photo_url)}
                    alt="Profile"
                    className="w-24 h-24 object-cover rounded-lg mx-auto cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => setSelectedImage(getImageUrl(client.client_photo_url))}
                  />
                </div>
              </div>
            )}
            {client.aadhar_photo_url && (
              <div className="text-center">
                <label className="text-sm font-medium text-gray-500">Aadhar Card</label>
                <div className="mt-2">
                  <img
                    src={getImageUrl(client.aadhar_photo_url)}
                    alt="Aadhar"
                    className="w-24 h-24 object-cover rounded-lg mx-auto cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => setSelectedImage(getImageUrl(client.aadhar_photo_url))}
                  />
                </div>
              </div>
            )}
            {client.pan_photo_url && (
              <div className="text-center">
                <label className="text-sm font-medium text-gray-500">PAN Card</label>
                <div className="mt-2">
                  <img
                    src={getImageUrl(client.pan_photo_url)}
                    alt="PAN"
                    className="w-24 h-24 object-cover rounded-lg mx-auto cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => setSelectedImage(getImageUrl(client.pan_photo_url))}
                  />
                </div>
              </div>
            )}
          </div>
          {!client.client_photo_url && !client.aadhar_photo_url && !client.pan_photo_url && (
            <p className="text-gray-500 text-center py-4">No documents uploaded</p>
          )}
        </CardContent>
      </Card>

      {/* Grid Layout for Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Investments */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-green-600 flex items-center justify-between">
              Active Investments ({activeInvestments.length})
              {/* <Button variant="outline" size="sm" onClick={() => navigate('/investments')}>
                <Eye className="h-4 w-4 mr-2" />View All
              </Button> */}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeInvestments.length > 0 ? (
              <div className="max-h-80 overflow-y-auto scrollbar-hide space-y-3">
                {activeInvestments.map((investment) => (
                  <div key={investment.id} className="border rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-sm">{investment.scheme.name}</h4>
                        <Badge
                          variant="outline"
                          className={`text-xs mt-1 ${investment.applicant_type === 'primary'
                            ? 'text-blue-600 border-blue-200'
                            : 'text-purple-600 border-purple-200'
                            }`}
                        >
                          {investment.applicant_type === 'primary' ? 'Primary Applicant' : 'Secondary Applicant'}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Badge variant="default" className="text-xs">Active</Badge>
                        <Button variant="ghost" size="sm" onClick={() => navigate(`/investments/${investment.id}`)} className="h-6 w-6 p-0">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div><span className="text-gray-500">Amount:</span> ₹{investment.amount.toLocaleString()}</div>
                      <div><span className="text-gray-500">Maturity:</span> ₹{investment.maturity_amount.toLocaleString()}</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4 text-sm">No active investments</p>
            )}
          </CardContent>
        </Card>

        {/* Inactive Investments */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-gray-600 flex items-center justify-between">
              Inactive Investments ({inactiveInvestments.length})
              {/* <Button variant="outline" size="sm" onClick={() => navigate('/investments')}>
                <Eye className="h-4 w-4 mr-2" />View All
              </Button> */}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {inactiveInvestments.length > 0 ? (
              <div className="max-h-80 overflow-y-auto scrollbar-hide space-y-3">
                {inactiveInvestments.map((investment) => (
                  <div key={investment.id} className="border rounded-lg p-3 bg-gray-50">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-sm">{investment.scheme.name}</h4>
                        <Badge
                          variant="outline"
                          className={`text-xs mt-1 ${investment.applicant_type === 'primary'
                              ? 'text-blue-600 border-blue-200'
                              : 'text-purple-600 border-purple-200'
                            }`}
                        >
                          {investment.applicant_type === 'primary' ? 'Primary Applicant' : 'Secondary Applicant'}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Badge variant="secondary" className="text-xs">{investment.status}</Badge>
                        <Button variant="ghost" size="sm" onClick={() => navigate(`/investments/${investment.id}`)} className="h-6 w-6 p-0">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div><span className="text-gray-500">Amount:</span> ₹{investment.amount.toLocaleString()}</div>
                      <div><span className="text-gray-500">Maturity:</span> ₹{investment.maturity_amount.toLocaleString()}</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4 text-sm">No inactive investments</p>
            )}
          </CardContent>
        </Card>

        {/* SB Accounts */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center justify-between">
              SB Accounts ({sbAccounts.length})

            </CardTitle>
          </CardHeader>
          <CardContent>
            {sbAccounts.length > 0 ? (
              <div className="max-h-80 overflow-y-auto scrollbar-hide space-y-3">
                {sbAccounts.map((account) => (
                  <div key={account.id} className="border rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-sm">{account.sb_account_number}</h4>
                      <Badge variant={account.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                        {account.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div><span className="text-gray-500">Type:</span> <span className="capitalize">{account.account_type}</span></div>
                      <div><span className="text-gray-500">Role:</span> <span className="capitalize">{account.client_role || 'N/A'}</span></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4 text-sm">No SB accounts found</p>
            )}
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center justify-between">
              Recent Transactions
              {/* <Button variant="outline" size="sm" onClick={() => navigate('/transactions')}>
                <Eye className="h-4 w-4 mr-2" />View All
              </Button> */}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {transactions.length > 0 ? (
              <div className="max-h-80 overflow-y-auto scrollbar-hide space-y-3">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="border rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-sm">{transaction.investment.scheme.name}</h4>
                      <Button variant="ghost" size="sm" onClick={() => navigate(`/transactions/${transaction.id}`)} className="h-6 w-6 p-0">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div><span className="text-gray-500">Amount:</span> ₹{transaction.amount.toLocaleString()}</div>
                      <div><span className="text-gray-500">Type:</span> {transaction.amount_type}</div>
                    </div>
                    <div className="mt-1 text-xs text-gray-500">Ref: {transaction.reference_number}</div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4 text-sm">No transactions found</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Nominees - Full Width */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center justify-between">
            Nominees ({nominees.length})
            <Button variant="outline" size="sm" onClick={() => setShowAddNominee(!showAddNominee)}>
              <Plus className="h-4 w-4 mr-2" />Add Nominee
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {showAddNominee && (
            <div className="border p-4 rounded-lg bg-gray-50">
              <h4 className="font-medium mb-4">Add New Nominee</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Name *</label>
                  <input type="text" className="w-full px-3 py-2 border rounded-md" placeholder="Enter nominee name" value={newNominee.name} onChange={(e) => setNewNominee({ ...newNominee, name: capitalizeFirstLetter(e.target.value) })} />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Relation *</label>
                  <input type="text" className="w-full px-3 py-2 border rounded-md" placeholder="Enter relation" value={newNominee.relation} onChange={(e) => setNewNominee({ ...newNominee, relation: capitalizeFirstLetter(e.target.value) })} />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Birthdate</label>
                  <input type="date" className="w-full px-3 py-2 border rounded-md" value={newNominee.birthdate} onChange={(e) => setNewNominee({ ...newNominee, birthdate: e.target.value })} />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Document (Optional)</label>
                  <input type="file" accept="image/*,.pdf,.doc,.docx" className="w-full px-3 py-2 border rounded-md cursor-pointer" onChange={(e) => setNewNominee({ ...newNominee, document: e.target.files?.[0] || null })} />
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <Button onClick={handleAddNominee} size="sm">Add Nominee</Button>
                <Button variant="outline" onClick={() => setShowAddNominee(false)} size="sm">Cancel</Button>
              </div>
            </div>
          )}
          {nominees.length > 0 ? (
            <div className="max-h-60 overflow-y-auto scrollbar-hide space-y-3">
              {nominees.map((nominee) => (
                <div key={nominee.id} className="border rounded-lg p-3">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 text-sm">
                    <div><span className="text-gray-500">Name:</span> <span className="font-medium">{nominee.name}</span></div>
                    <div><span className="text-gray-500">Relation:</span> {nominee.relation}</div>
                    <div><span className="text-gray-500">Birthdate:</span> {nominee.birthdate ? new Date(nominee.birthdate).toLocaleDateString() : 'Not provided'}</div>
                    <div>
                      <span className="text-gray-500">Document:</span>
                      {nominee.document_url ? (
                        <Button variant="ghost" size="sm" className="ml-2 h-auto p-0 text-blue-600 hover:text-blue-700" onClick={() => { const { data } = supabase.storage.from('client-documents').getPublicUrl(nominee.document_url!); window.open(data.publicUrl, '_blank'); }}>
                          <FileText className="h-4 w-4 mr-1" />View
                        </Button>
                      ) : (
                        <span className="ml-2 text-gray-400">No document</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">No nominees added</p>
          )}
        </CardContent>
      </Card>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedImage}
              alt="Document"
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-2 transition-all"
            >
              ✕
            </button>
          </div>
        </div>
      )}
      <style>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default ClientDetail;
