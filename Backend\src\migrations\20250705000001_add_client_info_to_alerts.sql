-- Add client_id column to alerts table for manual SMS alerts
ALTER TABLE public.alerts
ADD COLUMN client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL;

-- Update the alerts table to allow null investment_id for manual SMS alerts
ALTER TABLE public.alerts
ALTER COLUMN investment_id DROP NOT NULL;

-- Add index for better performance on client_id lookups
CREATE INDEX idx_alerts_client_id ON public.alerts(client_id);
