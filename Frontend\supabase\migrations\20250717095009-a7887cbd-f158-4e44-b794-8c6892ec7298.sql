
-- Enable pg_cron extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create or replace the cron job to run send-email function every midnight
SELECT cron.schedule(
  'send-email-alerts-midnight',
  '0 0 * * *', -- Every day at midnight (00:00)
  $$
  SELECT
    net.http_post(
        url:='https://ozrjdhnfcfjzvetdykwe.supabase.co/functions/v1/send-email',
        headers:='{"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im96cmpkaG5mY2ZqenZldGR5a3dlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MzQxMzEsImV4cCI6MjA2NjUxMDEzMX0.pC0D_u2fTrMEjZ2K_W3vp4NC6KBkvF-PTdKYSsJ147k"}'::jsonb,
        body:='{"source": "cron"}'::jsonb
    ) as request_id;
  $$
);

-- Create a function to manually check cron jobs (useful for debugging)
CREATE OR REPLACE FUNCTION public.check_cron_jobs()
RETURNS TABLE(jobid bigint, schedule text, command text, nodename text, nodeport integer, database text, username text, active boolean, jobname text)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM cron.job;
$$;

-- Grant execute permission to authenticated users for the check function
GRANT EXECUTE ON FUNCTION public.check_cron_jobs() TO authenticated;
