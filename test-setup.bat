@echo off
echo 🧪 Testing Full Stack Application Setup...

REM Check if required files exist
echo 📋 Checking required files...

set missing_files=0

if not exist .env (
    echo ❌ Missing: .env
    set missing_files=1
)

if not exist docker-compose.yml (
    echo ❌ Missing: docker-compose.yml
    set missing_files=1
)

if not exist Frontend\Dockerfile (
    echo ❌ Missing: Frontend\Dockerfile
    set missing_files=1
)

if not exist Backend\Dockerfile (
    echo ❌ Missing: Backend\Dockerfile
    set missing_files=1
)

if not exist Frontend\package.json (
    echo ❌ Missing: Frontend\package.json
    set missing_files=1
)

if not exist Backend\package.json (
    echo ❌ Missing: Backend\package.json
    set missing_files=1
)

if %missing_files%==1 (
    echo ❌ Some required files are missing
    pause
    exit /b 1
)

echo ✅ All required files present

REM Check if Docker is running
echo 🐳 Checking Docker...
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

echo ✅ Docker is running

REM Validate docker-compose file
echo 📝 Validating docker-compose.yml...
docker-compose config >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose.yml has syntax errors
    docker-compose config
    pause
    exit /b 1
)

echo ✅ docker-compose.yml is valid

REM Test building images (without starting)
echo 🔨 Testing Docker image builds...

echo    Building Frontend image...
docker build -t test-frontend ./Frontend >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend Docker build failed
    pause
    exit /b 1
)

echo    Building Backend image...
docker build -t test-backend ./Backend >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend Docker build failed
    pause
    exit /b 1
)

echo ✅ Docker images build successfully

REM Clean up test images
echo 🧹 Cleaning up test images...
docker rmi test-frontend test-backend >nul 2>&1

echo.
echo ✅ Setup validation completed successfully!
echo.
echo 🚀 Your application is ready to start:
echo    start.bat     (Windows)
echo    ./start.sh    (Linux/Mac)
echo.
echo 🔧 For development mode:
echo    dev.bat       (Windows)
echo    ./dev.sh      (Linux/Mac)
echo.
pause
