
-- Add new columns to investments table for scheme details preservation
ALTER TABLE public.investments 
ADD COLUMN scheme_name character varying NOT NULL DEFAULT '',
ADD COLUMN payout_status character varying DEFAULT 'paid',
ADD COLUMN tenure_months integer,
ADD COLUMN interest_rate numeric NOT NULL DEFAULT 0,
ADD COLUMN lock_in_period_months integer NOT NULL DEFAULT 0,
ADD COLUMN scheme_code character varying NOT NULL DEFAULT '',
ADD COLUMN interest_type character varying NOT NULL DEFAULT '',
ADD COLUMN compounding_frequency character varying,
ADD COLUMN min_amount numeric,
ADD COLUMN max_amount numeric,
ADD COLUMN commission_percentage numeric,
ADD COLUMN supports_sip boolean;

-- Remove the default values after adding the columns (keeping only the intended defaults)
ALTER TABLE public.investments 
ALTER COLUMN scheme_name DROP DEFAULT,
ALTER COLUMN interest_rate DROP DEFAULT,
ALTER COLUMN lock_in_period_months DROP DEFAULT,
ALTER COLUMN scheme_code DROP DEFAULT,
ALTER COLUMN interest_type DROP DEFAULT;
