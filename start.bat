@echo off
echo 🚀 Starting Full Stack Application...

REM Check if .env file exists
if not exist .env (
    echo ❌ .env file not found. Please copy .env.example to .env and configure your environment variables.
    pause
    exit /b 1
)

echo 📋 Environment file found

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

echo 🐳 Docker is running

REM Build and start all services
echo 🔨 Building and starting services...
docker-compose up --build -d

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM Check service status
echo 📊 Service Status:
docker-compose ps

echo.
echo ✅ Application started successfully!
echo.
echo 🌐 Access your applications:
echo    Frontend:        http://localhost:3000
echo    Backend API:     http://localhost:3002
echo    Supabase Studio: http://localhost:3001
echo    Supabase API:    http://localhost:8000
echo.
echo 📝 To view logs: docker-compose logs -f [service-name]
echo 🛑 To stop: docker-compose down
echo 🗑️  To reset: docker-compose down -v --remove-orphans
echo.
pause
