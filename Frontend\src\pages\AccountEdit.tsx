import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { ArrowLeft, Save } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

const accountEditSchema = z.object({
  sb_account_number: z.string()
    .min(1, 'Account number is required')
    .length(11, 'Account number must be exactly 11 digits')
    .regex(/^\d{11}$/, 'Account number must contain only digits'),
  account_type: z.enum(['single', 'joint']),
  status: z.enum(['active', 'closed', 'suspended']),
  opening_balance: z.string().optional(),
  opened_at: z.string().optional(),
  remarks: z.string().optional(),
});

type AccountEditData = z.infer<typeof accountEditSchema>;

const AccountEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [accountNumberError, setAccountNumberError] = useState<string | null>(null);
  const [isCheckingAccount, setIsCheckingAccount] = useState(false);
  const [isExistingAccount, setIsExistingAccount] = useState(false);

  const form = useForm<AccountEditData>({
    resolver: zodResolver(accountEditSchema),
    defaultValues: {
      sb_account_number: '',
      account_type: 'single',
      status: 'active',
      opening_balance: '',
      opened_at: '',
      remarks: '',
    },
  });

  useEffect(() => {
    if (id) {
      fetchAccount();
    }
  }, [id]);

  const fetchAccount = async () => {
    try {
      const { data, error } = await supabase
        .from('sb_accounts')
        .select('*')
        .eq('id', id)
        .eq('is_deleted', false)
        .single();

      if (error) throw error;

      // Format date for input
      const openedAt = data.opened_at ? new Date(data.opened_at).toISOString().split('T')[0] : '';
      
      // Check if this is an existing account
      setIsExistingAccount(data.already_opened || false);

   form.reset({
  sb_account_number: data.sb_account_number,
  account_type: data.account_type as 'single' | 'joint',
  status: data.status as 'active' | 'closed' | 'suspended',
  opening_balance: data.opening_balance?.toString() || '',
  opened_at: openedAt,
  remarks: data.remarks || '',
});

    } catch (error) {
      console.error('Error fetching account:', error);
      toast({
        title: "Error",
        description: "Failed to fetch account details",
        variant: "destructive",
      });
      navigate('/accounts');
    } finally {
      setInitialLoading(false);
    }
  };

  const validateAccountNumber = async (accountNumber: string) => {
    if (!accountNumber.trim()) {
      setAccountNumberError(null);
      return;
    }

    if (accountNumber.length !== 11) {
      setAccountNumberError('Account number must be exactly 11 digits');
      return;
    }

    if (!/^\d{11}$/.test(accountNumber)) {
      setAccountNumberError('Account number must contain only digits');
      return;
    }

    setIsCheckingAccount(true);
    try {
      const { data: existingAccount } = await supabase
        .from('sb_accounts')
        .select('id')
        .eq('sb_account_number', accountNumber)
        .eq('is_deleted', false)
        .neq('id', id)
        .single();

      if (existingAccount) {
        setAccountNumberError('Account number already exists');
      } else {
        setAccountNumberError(null);
      }
    } catch (error) {
      if (error.code !== 'PGRST116') {
        setAccountNumberError('Error checking account number');
      } else {
        setAccountNumberError(null);
      }
    } finally {
      setIsCheckingAccount(false);
    }
  };

  const onSubmit = async (data: AccountEditData) => {
    if (accountNumberError) {
      toast({
        title: "Invalid Account Number",
        description: accountNumberError,
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const updateData = {
        sb_account_number: data.sb_account_number,
        account_type: data.account_type,
        status: data.status,
        opening_balance: data.opening_balance ? parseFloat(data.opening_balance) : null,
        opened_at: data.opened_at || null,
        remarks: data.remarks || null,
      };

      const { error } = await supabase
        .from('sb_accounts')
        .update(updateData)
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Account updated successfully",
      });

      navigate(`/accounts/${id}`);
    } catch (error) {
      console.error('Error updating account:', error);
      toast({
        title: "Error",
        description: "Failed to update account",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading account details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => navigate(`/accounts/${id}`)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">Edit Account</h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="sb_account_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Number *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter 11-digit account number" 
                        maxLength={11}
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, '');
                          field.onChange(value);
                          validateAccountNumber(value);
                        }}
                      />
                    </FormControl>
                    {isCheckingAccount && (
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                        Checking account number...
                      </div>
                    )}
                    {accountNumberError && (
                      <p className="text-sm text-red-500 mt-1">{accountNumberError}</p>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="account_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Type *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="single">Single</SelectItem>
                          <SelectItem value="joint">Joint</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="closed">Closed</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {!isExistingAccount && (
                  <FormField
                    control={form.control}
                    name="opening_balance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Opening Balance</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter opening balance"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="opened_at"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Opened Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="remarks"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Remarks</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any remarks"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(`/accounts/${id}`)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Updating...' : 'Update Account'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default AccountEdit;