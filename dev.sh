#!/bin/bash

# Development Mode Script
# This script starts the application in development mode with additional services

echo "🔧 Starting Full Stack Application in Development Mode..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure your environment variables."
    exit 1
fi

# Load environment variables
source .env

echo "📋 Environment loaded"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "🐳 Docker is running"

# Start with development compose file if it exists
if [ -f docker/dev/docker-compose.dev.yml ]; then
    echo "🔨 Building and starting services with development configuration..."
    docker-compose -f docker-compose.yml -f docker/dev/docker-compose.dev.yml up --build -d
else
    echo "🔨 Building and starting services..."
    docker-compose up --build -d
fi

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check service status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "✅ Development environment started successfully!"
echo ""
echo "🌐 Access your applications:"
echo "   Frontend:        http://localhost:3000"
echo "   Backend API:     http://localhost:3002"
echo "   Supabase Studio: http://localhost:3001"
echo "   Supabase API:    http://localhost:8000"
if [ -f docker/dev/docker-compose.dev.yml ]; then
    echo "   Mail Server:     http://localhost:9000 (Inbucket)"
    echo "   Meta API:        http://localhost:5555"
fi
echo ""
echo "📝 Development Commands:"
echo "   View logs:       docker-compose logs -f [service-name]"
echo "   Restart service: docker-compose restart [service-name]"
echo "   Shell access:    docker-compose exec [service-name] sh"
echo "   Stop:            docker-compose down"
echo "   Reset:           ./reset.sh"
