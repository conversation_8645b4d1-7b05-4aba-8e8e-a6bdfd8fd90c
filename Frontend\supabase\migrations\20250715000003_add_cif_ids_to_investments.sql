-- Add CIF ID fields to investments table for primary and secondary applicants
ALTER TABLE public.investments 
ADD COLUMN primary_applicant_cif_id VARCHAR(50),
ADD COLUMN secondary_applicant_cif_id VARCHAR(50);

-- Add indexes for better performance
CREATE INDEX idx_investments_primary_cif ON public.investments(primary_applicant_cif_id);
CREATE INDEX idx_investments_secondary_cif ON public.investments(secondary_applicant_cif_id);

-- Update existing investments with CIF IDs from clients table
UPDATE public.investments 
SET primary_applicant_cif_id = (
  SELECT cif_id 
  FROM public.clients 
  WHERE clients.id = investments.client_id
);

UPDATE public.investments 
SET secondary_applicant_cif_id = (
  SELECT cif_id 
  FROM public.clients 
  WHERE clients.id = investments.second_applicant_id
)
WHERE second_applicant_id IS NOT NULL;
