import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Check, ChevronDown, TrendingUp, Search, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';

interface Investment {
  id: string;
  scheme_name: string;
  scheme_code: string;
  amount: number;
  client_name: string;
}

interface InvestmentSelectorProps {
  selectedInvestment: Investment | null;
  onInvestmentSelect: (investment: Investment | null) => void;
  disabled?: boolean;
}

const InvestmentSelector: React.FC<InvestmentSelectorProps> = ({
  selectedInvestment,
  onInvestmentSelect,
  disabled = false,
}) => {
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchInvestments();
  }, []);

  const fetchInvestments = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          id,
          scheme_name,
          scheme_code,
          amount,
          clients!investments_client_id_fkey (
            first_name,
            last_name
          )
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching investments:', error);
        throw error;
      }

      console.log('Fetched investments:', data);

      const transformedData = (data || []).map((investment) => ({
        id: investment.id,
        scheme_name: investment.scheme_name || '',
        scheme_code: investment.scheme_code || '',
        amount: investment.amount || 0,
        client_name: investment.clients
          ? `${investment.clients.first_name || ''} ${investment.clients.last_name || ''}`.trim()
          : 'Unknown Client',
      }));

      setInvestments(transformedData);
    } catch (error) {
      console.error('Error fetching investments:', error);
      setInvestments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInvestmentSelect = (investment: Investment) => {
    onInvestmentSelect(investment);
    setOpen(false);
    setSearchTerm('');
  };

  const handleClear = () => {
    onInvestmentSelect(null);
    setSearchTerm('');
  };

  // Helper function to get display name
  const getInvestmentDisplayName = (investment: Investment) => {
    return `${investment.client_name} - ${investment.scheme_name}`;
  };

  // Filter investments based on search term
  const filteredInvestments = investments.filter(investment => {
    const searchLower = searchTerm.toLowerCase();
    const clientName = (investment.client_name || '').toLowerCase();
    const schemeName = (investment.scheme_name || '').toLowerCase();
    const schemeCode = (investment.scheme_code || '').toLowerCase();
    
    return clientName.includes(searchLower) || 
           schemeName.includes(searchLower) || 
           schemeCode.includes(searchLower);
  });

  return (
    <div className="relative w-full">
      <Button
        type="button"
        variant="outline"
        role="combobox"
        aria-expanded={open}
        disabled={disabled}
        onClick={() => setOpen(!open)}
        className="w-full justify-between h-10 bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {selectedInvestment ? (
          <div className="flex items-center gap-2 truncate">
            <TrendingUp className="h-4 w-4 text-orange-500" />
            <span className="truncate text-gray-900">
              {getInvestmentDisplayName(selectedInvestment)}
            </span>
          </div>
        ) : (
          <span className="text-gray-500">Select an investment...</span>
        )}
        <div className="flex items-center gap-1">
          {selectedInvestment && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleClear();
              }}
              className="p-1 hover:bg-gray-200 rounded"
            >
              <X className="h-3 w-3 text-gray-400" />
            </button>
          )}
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </Button>

      {open && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-hidden">
          {/* Search Input */}
          <div className="p-2 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search investments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-9 text-sm"
                autoFocus
              />
            </div>
          </div>

          {/* Investment List */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-sm text-gray-500">
                Loading investments...
              </div>
            ) : filteredInvestments.length === 0 ? (
              <div className="p-4 text-center text-sm text-gray-500">
                {searchTerm ? 'No investments found.' : 'No investments available.'}
              </div>
            ) : (
              filteredInvestments.map((investment) => (
                <button
                  key={investment.id}
                  type="button"
                  onClick={() => handleInvestmentSelect(investment)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-3 text-left hover:bg-gray-50 transition-colors",
                    selectedInvestment?.id === investment.id && "bg-orange-50"
                  )}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-orange-100">
                    <TrendingUp className="h-4 w-4 text-orange-600" />
                  </div>
                  <div className="flex flex-col flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <span className="font-medium text-gray-900 text-sm truncate">
                        {investment.client_name}
                      </span>
                      <span className="text-sm font-medium text-green-600 shrink-0 ml-2">
                        ₹{investment.amount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 truncate">
                        {investment.scheme_name}
                      </span>
                      <span className="text-xs text-gray-400 shrink-0 ml-2">
                        {investment.scheme_code}
                      </span>
                    </div>
                  </div>
                  {selectedInvestment?.id === investment.id && (
                    <Check className="h-4 w-4 text-orange-600 shrink-0" />
                  )}
                </button>
              ))
            )}
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {open && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setOpen(false)}
        />
      )}
    </div>
  );
};

export default InvestmentSelector;