
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import InvestmentSelector from '@/components/transaction/InvestmentSelector';

interface Investment {
  id: string;
  scheme_name: string;
  scheme_code: string;
  amount: number;
  client_name: string;
}

interface SBAccount {
  id: string;
  sb_account_number: string;
  account_type: string;
  status: string;
}
interface LocationState {
  prefilledData?: {
    investment_id?: string;
    amount_type?: string;
    amount?: number;
    transaction_date?: string;
    remark?: string;
    isTransfer?: boolean;
  };
}


const TransactionForm: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [selectedInvestment, setSelectedInvestment] = useState<Investment | null>(null);
  const [amount, setAmount] = useState<string>('');
  const [amountType, setAmountType] = useState<string>('');
  const [customAmountType, setCustomAmountType] = useState<string>('');
  const [transactionDate, setTransactionDate] = useState<Date>(new Date());
  const [referenceNumber, setReferenceNumber] = useState<string>('');
  const [remark, setRemark] = useState<string>('');
  const [isTransfer, setIsTransfer] = useState(false);
  const [paymentMode, setPaymentMode] = useState<string>('');
  const [selectedSBAccount, setSelectedSBAccount] = useState<string>('');
  const [sbAccounts, setSbAccounts] = useState<SBAccount[]>([]);
  const [amountError, setAmountError] = useState<string | null>(null);
  const [investmentError, setInvestmentError] = useState<string | null>(null);
  const [amountTypeError, setAmountTypeError] = useState<string | null>(null);
  const [customAmountTypeError, setCustomAmountTypeError] = useState<string | null>(null);
  const [sbAccountError, setSbAccountError] = useState<string | null>(null);

  const generateReferenceNumber = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `TXN-${timestamp}-${random}`;
  };

  useEffect(() => {
    if (!referenceNumber) {
      setReferenceNumber(generateReferenceNumber());
    }

    // Fetch SB accounts
    fetchSBAccounts();

    // Handle pre-filled data from maturity transfer
    const state = location.state as LocationState;
    if (state?.prefilledData) {
      const { prefilledData } = state;
      if (prefilledData.investment_id) {
        fetchInvestmentById(prefilledData.investment_id);
      }
      if (prefilledData.amount_type) setAmountType(prefilledData.amount_type);
      if (prefilledData.amount) setAmount(prefilledData.amount.toString());
      if (prefilledData.transaction_date) setTransactionDate(new Date(prefilledData.transaction_date));
      if (prefilledData.remark) setRemark(prefilledData.remark);
      if (prefilledData.isTransfer) setIsTransfer(true);
    }
  }, [location.state]);

  const fetchSBAccounts = async () => {
    try {
      const { data, error } = await supabase
        .from('sb_accounts')
        .select('id, sb_account_number, account_type, status')
        .eq('status', 'active')
        .eq('is_deleted', false);

      if (error) throw error;
      setSbAccounts(data || []);
    } catch (error) {
      console.error('Error fetching SB accounts:', error);
    }
  };

  const fetchInvestmentById = async (investmentId: string) => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          id,
          scheme_name,
          scheme_code,
          amount,
          clients!investments_client_id_fkey (
            first_name,
            last_name
          )
        `)
        .eq('id', investmentId)
        .single();

      if (error) throw error;

      if (data) {
        setSelectedInvestment({
          id: data.id,
          scheme_name: data.scheme_name,
          scheme_code: data.scheme_code,
          amount: data.amount,
          client_name: `${data.clients?.first_name} ${data.clients?.last_name}`
        });
      }
    } catch (error) {
      console.error('Error fetching investment:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields before submission
    let hasErrors = false;

    if (!selectedInvestment) {
      setInvestmentError('Please select an investment');
      hasErrors = true;
    }

    if (!amount || amount.trim() === '') {
      setAmountError('Amount is required');
      hasErrors = true;
    } else {
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount)) {
        setAmountError('Amount must be a valid number');
        hasErrors = true;
      } else if (numericAmount < 0) {
        setAmountError('Amount cannot be negative');
        hasErrors = true;
      } else if (numericAmount === 0) {
        setAmountError('Amount must be greater than zero');
        hasErrors = true;
      }
    }

    if (!amountType) {
      setAmountTypeError('Please select an amount type');
      hasErrors = true;
    }

    if (amountType === 'other' && !customAmountType.trim()) {
      setCustomAmountTypeError('Please specify the custom amount type');
      hasErrors = true;
    }

    if (paymentMode === 'sb_account' && !selectedSBAccount) {
      setSbAccountError('Please select an SB account');
      hasErrors = true;
    }

    if (hasErrors) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors below and try again",
        variant: "destructive",
      });
      return;
    }

    const finalAmountType = amountType === 'other' ? customAmountType : amountType;

    setLoading(true);
    try {
      const transactionData = {
        investment_id: selectedInvestment.id,
        amount_type: finalAmountType,
        amount: parseFloat(amount),
        transaction_date: transactionDate.toISOString().split('T')[0],
        reference_number: paymentMode === 'cash' ? null : (referenceNumber || generateReferenceNumber()),
        remark: remark || null,
        payment_mode: paymentMode || null,
        sb_account_id: paymentMode === 'sb_account' ? selectedSBAccount : null,
      };

      console.log('Creating transaction with data:', transactionData);

      const { data, error } = await supabase
        .from('transactions')
        .insert([transactionData])
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Transaction created successfully:', data);

      // If this is a maturity transfer, update the investment status
      if (isTransfer && finalAmountType === 'withdrawal') {
        const { error: updateError } = await supabase
          .from('investments')
          .update({ status: 'withdrawn' })
          .eq('id', selectedInvestment.id);

        if (updateError) {
          console.error('Error updating investment status:', updateError);
        }
      }

      toast({
        title: "Success",
        description: "Transaction created successfully",
      });

      navigate('/transactions');
    } catch (error) {
      console.error('Error creating transaction:', error);
      toast({
        title: "Error",
        description: `Failed to create transaction: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => navigate('/transactions')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isTransfer ? 'Transfer Maturity Amount' : 'Add New Transaction'}
        </h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Transaction Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Investment Selection */}
            <div className="space-y-2">
              <Label>Investment *</Label>
              <InvestmentSelector
                selectedInvestment={selectedInvestment}
                onInvestmentSelect={(investment) => {
                  setSelectedInvestment(investment);
                  setInvestmentError(investment ? null : 'Please select an investment');
                }}
                disabled={isTransfer}
              />
              {investmentError && (
                <p className="text-sm text-red-500 mt-1">{investmentError}</p>
              )}
            </div>

            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount">Amount *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="Enter amount"
                value={amount}
                onChange={(e) => {
                  const input = e.target.value;
                  setAmount(input);

                  // Live validation for amount
                  if (!input || input.trim() === '') {
                    setAmountError('Amount is required');
                  } else {
                    const numericAmount = parseFloat(input);
                    if (isNaN(numericAmount)) {
                      setAmountError('Amount must be a valid number');
                    } else if (numericAmount < 0) {
                      setAmountError('Amount cannot be negative');
                    } else if (numericAmount === 0) {
                      setAmountError('Amount must be greater than zero');
                    } else {
                      setAmountError(null);
                    }
                  }
                }}

                disabled={isTransfer}
              />
              {amountError && (
                <p className="text-sm text-red-500 mt-1">{amountError}</p>
              )}
            </div>

            {/* Amount Type */}
            <div className="space-y-2">
              <Label>Amount Type *</Label>
              <Select value={amountType} onValueChange={(value) => {
                setAmountType(value);
                setAmountTypeError(value ? null : 'Please select an amount type');
                if (value !== 'other') {
                  setCustomAmountTypeError(null);
                }
              }} required disabled={isTransfer}>
                <SelectTrigger>
                  <SelectValue placeholder="Select amount type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="investment">Investment Amount</SelectItem>
                  <SelectItem value="interest_payout">Interest Payout</SelectItem>
                  <SelectItem value="maturity_payout">Maturity Payout</SelectItem>
                  <SelectItem value="reinvestment">Reinvestment</SelectItem>
                  <SelectItem value="penalty">Penalty</SelectItem>
                  <SelectItem value="withdrawal">Withdrawal</SelectItem>
                  <SelectItem value="commission">Commission</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              {amountTypeError && (
                <p className="text-sm text-red-500 mt-1">{amountTypeError}</p>
              )}
            </div>

            {/* Custom Amount Type */}
            {amountType === 'other' && (
              <div className="space-y-2">
                <Label htmlFor="customAmountType">Custom Amount Type *</Label>
                <Input
                  id="customAmountType"
                  placeholder="Enter custom amount type"
                  value={customAmountType}
                  onChange={(e) => {
                    const value = e.target.value;
                    setCustomAmountType(value);
                    setCustomAmountTypeError(value.trim() ? null : 'Please specify the custom amount type');
                  }}
                  required
                />
                {customAmountTypeError && (
                  <p className="text-sm text-red-500 mt-1">{customAmountTypeError}</p>
                )}
              </div>
            )}

            {/* Payment Mode */}
            <div className="space-y-2">
              <Label>Payment Mode</Label>
              <Select value={paymentMode} onValueChange={setPaymentMode}>
                <SelectTrigger>
                  <SelectValue placeholder="Select payment mode (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sb_account">SB Account</SelectItem>
                  <SelectItem value="cheque">Cheque</SelectItem>
                  <SelectItem value="ecs">ECS</SelectItem>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="rtgs/neft">RTGS/NEFT</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* SB Account Selection */}
            {paymentMode === 'sb_account' && (
              <div className="space-y-2">
                <Label>SB Account *</Label>
                <Select value={selectedSBAccount} onValueChange={(value) => {
                  setSelectedSBAccount(value);
                  setSbAccountError(value ? null : 'Please select an SB account');
                }} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select SB account" />
                  </SelectTrigger>
                  <SelectContent>
                    {sbAccounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.sb_account_number} ({account.account_type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {sbAccountError && (
                  <p className="text-sm text-red-500 mt-1">{sbAccountError}</p>
                )}
              </div>
            )}

            {/* Transaction Date */}
            <div className="space-y-2">
              <Label>Transaction Date *</Label>
              <DatePicker
                date={transactionDate}
                onSelect={(date) => setTransactionDate(date || new Date())}
                placeholder="Select transaction date"
              />
            </div>

            {/* Reference Number */}
            <div className="space-y-2">
              <Label htmlFor="referenceNumber">
                Reference Number
                {(paymentMode === 'cheque' || paymentMode === 'ecs') && ' *'}
                {paymentMode === 'cash' && ' (Not Required)'}
                {(paymentMode === 'rtgs/neft' || paymentMode === 'sb_account') && ' (Optional)'}
              </Label>
              <Input
                id="referenceNumber"
                placeholder={
                  paymentMode === 'cash'
                    ? "Reference number not required for cash"
                    : paymentMode === 'cheque'
                      ? "Enter cheque number"
                      : paymentMode === 'ecs'
                        ? "Enter ECS reference number"
                        : "Auto-generated reference number"
                }
                value={referenceNumber}
                onChange={(e) => setReferenceNumber(e.target.value)}
                disabled={paymentMode === 'cash'}
              />
            </div>

            {/* Remark */}
            <div className="space-y-2">
              <Label htmlFor="remark">Remark (Optional)</Label>
              <Textarea
                id="remark"
                placeholder="Enter any remarks"
                value={remark}
                onChange={(e) => setRemark(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex justify-end gap-4">
              <Button type="button" variant="outline" onClick={() => navigate('/transactions')}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Creating...' : isTransfer ? 'Complete Transfer' : 'Create Transaction'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
};

export default TransactionForm;
