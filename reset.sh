#!/bin/bash

# Full Stack Application Reset Script
# This script completely resets the application by removing all containers, volumes, and data

echo "🗑️  Resetting Full Stack Application..."
echo "⚠️  WARNING: This will remove all data including database content!"
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 Stopping all services..."
    docker-compose down -v --remove-orphans
    
    echo "🧹 Removing unused Docker resources..."
    docker system prune -f
    
    echo "📁 Cleaning up volumes..."
    docker volume prune -f
    
    echo "✅ Reset completed successfully!"
    echo ""
    echo "🚀 To start fresh: ./start.sh"
else
    echo "❌ Reset cancelled."
fi
