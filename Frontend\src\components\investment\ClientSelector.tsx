import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Check, ChevronDown, User, Plus, Search, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string | null;
  mobile_number: string;
  village: string | null;
  cif_id: string | null;
}

interface ClientSelectorProps {
  selectedClient: Client | null;
  onClientSelect: (client: Client | null) => void;
  onAddClient?: () => void;
  required?: boolean;
  disabled?: boolean;
}

const ClientSelector: React.FC<ClientSelectorProps> = ({
  selectedClient,
  onClientSelect,
  onAddClient,
  required = false,
  disabled = false
}) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [addingClient, setAddingClient] = useState(false);
  const [newClient, setNewClient] = useState({
    first_name: '',
    last_name: '',
    mobile_number: '',
    cif_id: '',
    aadhar_number: '',
    pan_card_number: '',
    village: ''
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [liveErrors, setLiveErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, first_name, last_name, email, mobile_number, village, cif_id')
        .eq('is_deleted', false)
        .order('first_name');

      if (error) {
        console.error('Error fetching clients:', error);
        throw error;
      }

      console.log('Fetched clients:', data);
      // Ensure all clients have valid data
      const validClients = (data || []).map(client => ({
        id: client.id,
        first_name: client.first_name || '',
        last_name: client.last_name || '',
        email: client.email || '',
        mobile_number: client.mobile_number || '',
        village: client.village || null,
        cif_id: client.cif_id || null
      }));
      setClients(validClients);
    } catch (error) {
      console.error('Error fetching clients:', error);
      setClients([]);
    } finally {
      setLoading(false);
    }
  };

  const handleClientSelect = (client: Client) => {
    onClientSelect(client);
    setOpen(false);
    setSearchTerm('');
  };

  const handleClear = () => {
    onClientSelect(null);
    setSearchTerm('');
  };

  // Validation functions
  const validateField = (fieldName: string, value: string) => {
    const newLiveErrors = { ...liveErrors };

    switch (fieldName) {
      case 'first_name':
        if (!value.trim()) {
          newLiveErrors.first_name = 'First name is required';
        } else {
          delete newLiveErrors.first_name;
        }
        break;
      case 'mobile_number':
        if (!value.trim()) {
          newLiveErrors.mobile_number = 'Mobile number is required';
        } else if (!/^\d{10}$/.test(value)) {
          newLiveErrors.mobile_number = 'Mobile number must be exactly 10 digits';
        } else {
          delete newLiveErrors.mobile_number;
        }
        break;
      case 'cif_id':
        if (!value.trim()) {
          newLiveErrors.cif_id = 'CIF ID is required';
        } else {
          delete newLiveErrors.cif_id;
        }
        break;
      case 'aadhar_number':
        if (value && !/^\d{12}$/.test(value)) {
          newLiveErrors.aadhar_number = 'Aadhar number must be exactly 12 digits';
        } else {
          delete newLiveErrors.aadhar_number;
        }
        break;
      case 'pan_card_number':
        if (value && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value)) {
          newLiveErrors.pan_card_number = 'PAN Card format should be ********** (5 letters, 4 numbers, 1 letter)';
        } else {
          delete newLiveErrors.pan_card_number;
        }
        break;
    }

    setLiveErrors(newLiveErrors);
  };

  const checkDuplicate = async (fieldName: string, value: string) => {
    if (!value) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
      return;
    }

    const { data, error } = await supabase
      .from('clients')
      .select('id')
      .eq(fieldName, value)
      .eq('is_deleted', false)
      .limit(1);

    if (error) return;

    if (data && data.length > 0) {
      setValidationErrors(prev => ({
        ...prev,
        [fieldName]: `This ${fieldName.replace('_', ' ')} already exists`
      }));
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const handleFieldBlur = (fieldName: string, value: string) => {
    if (['mobile_number', 'pan_card_number', 'aadhar_number', 'cif_id'].includes(fieldName)) {
      checkDuplicate(fieldName, value);
    }
  };

  // Helper function to get display name
  const getClientDisplayName = (client: Client) => {
    const firstName = client.first_name || '';
    const lastName = client.last_name || '';
    return `${firstName} ${lastName}`.trim() || 'Unknown Client';
  };

  // Filter clients based on search term
  const filteredClients = clients.filter(client => {
    const searchLower = searchTerm.toLowerCase();
    const firstName = (client.first_name || '').toLowerCase();
    const lastName = (client.last_name || '').toLowerCase();
    const email = (client.email || '').toLowerCase();

    return firstName.includes(searchLower) ||
      lastName.includes(searchLower) ||
      email.includes(searchLower);
  });

  return (
    <div className="relative w-full">
      <Button
        type="button"
        variant="outline"
        role="combobox"
        aria-expanded={open}
        disabled={disabled}
        onClick={() => setOpen(!open)}
        className="w-full justify-between h-10 bg-white border border-gray-300 hover:bg-gray-50 text-left disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {selectedClient ? (
          <span className="text-gray-900 truncate">
            {getClientDisplayName(selectedClient)}
          </span>
        ) : (
          <span className="text-gray-500">Select a client...</span>
        )}
        <div className="flex items-center gap-1">
          {selectedClient && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleClear();
              }}
              className="p-1 hover:bg-gray-200 rounded"
            >
              <X className="h-3 w-3 text-gray-400" />
            </button>
          )}
          <ChevronDown className="h-4 w-4 shrink-0 text-gray-400" />
        </div>
      </Button>

      {open && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-hidden">
          {/* Search Input */}
          <div className="p-2 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-9 text-sm"
                autoFocus
              />
            </div>
          </div>

          {/* Client List */}
          <div className="max-h-64 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-sm text-gray-500">
                Loading clients...
              </div>
            ) : filteredClients.length === 0 ? (
              <div className="p-4 text-center text-sm text-gray-500">
                {searchTerm ? 'No clients found.' : 'No clients available.'}
              </div>
            ) : (
              filteredClients.map((client) => (
                <button
                  key={client.id}
                  type="button"
                  onClick={() => handleClientSelect(client)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-3 text-left hover:bg-gray-50 transition-colors",
                    selectedClient?.id === client.id && "bg-blue-50"
                  )}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex flex-col flex-1 min-w-0">
                    <span className="font-medium text-gray-900 text-sm truncate">
                      {getClientDisplayName(client)}
                    </span>
                    <span className="text-sm text-gray-500 truncate">
                      {client.email || 'No email'}
                    </span>
                  </div>
                  {selectedClient?.id === client.id && (
                    <Check className="h-4 w-4 text-blue-600 shrink-0" />
                  )}
                </button>
              ))
            )}
          </div>

          {/* Add New Client Button - Always at bottom */}
          <div className="border-t p-1 bg-gray-50">
            <button
              type="button"
              onClick={() => {
                setOpen(false);
                setShowAddModal(true);
              }}
              className="flex items-center gap-2 w-full px-3 py-3 text-sm text-blue-600 hover:bg-blue-100 rounded-sm font-medium"
            >
              <Plus className="h-4 w-4" />
              Add New Client
            </button>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {open && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setOpen(false)}
        />
      )}

      {/* Add Client Modal */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Client</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAddClient} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first_name">First Name *</Label>
                <Input
                  id="first_name"
                  value={newClient.first_name}
                  onChange={(e) => {
                    const value = e.target.value;
                    setNewClient(prev => ({ ...prev, first_name: value }));
                    validateField('first_name', value);
                  }}
                  onBlur={(e) => validateField('first_name', e.target.value)}
                  required
                  className={liveErrors.first_name ? 'border-red-500' : ''}
                />
                {liveErrors.first_name && (
                  <p className="text-sm text-red-500">{liveErrors.first_name}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  value={newClient.last_name}
                  onChange={(e) => setNewClient(prev => ({ ...prev, last_name: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="mobile_number">Mobile Number *</Label>
              <Input
                id="mobile_number"
                value={newClient.mobile_number}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 10);
                  setNewClient(prev => ({ ...prev, mobile_number: value }));
                  validateField('mobile_number', value);
                }}
                onBlur={(e) => handleFieldBlur('mobile_number', e.target.value)}
                placeholder="Enter 10-digit mobile number"
                maxLength={10}
                required
                className={liveErrors.mobile_number || validationErrors.mobile_number ? 'border-red-500' : ''}
              />
              {liveErrors.mobile_number && (
                <p className="text-sm text-red-500">{liveErrors.mobile_number}</p>
              )}
              {validationErrors.mobile_number && (
                <p className="text-sm text-red-500">{validationErrors.mobile_number}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="cif_id">CIF ID *</Label>
              <Input
                id="cif_id"
                value={newClient.cif_id}
                onChange={(e) => {
                  const value = e.target.value;
                  setNewClient(prev => ({ ...prev, cif_id: value }));
                  validateField('cif_id', value);
                }}
                onBlur={(e) => handleFieldBlur('cif_id', e.target.value)}
                placeholder="Enter CIF ID"
                required
                className={liveErrors.cif_id || validationErrors.cif_id ? 'border-red-500' : ''}
              />
              {liveErrors.cif_id && (
                <p className="text-sm text-red-500">{liveErrors.cif_id}</p>
              )}
              {validationErrors.cif_id && (
                <p className="text-sm text-red-500">{validationErrors.cif_id}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="aadhar_number">Aadhar Number (12 digits)</Label>
              <Input
                id="aadhar_number"
                value={newClient.aadhar_number}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 12);
                  setNewClient(prev => ({ ...prev, aadhar_number: value }));
                  validateField('aadhar_number', value);
                }}
                onBlur={(e) => handleFieldBlur('aadhar_number', e.target.value)}
                placeholder="123456789012 (optional)"
                maxLength={12}
                className={liveErrors.aadhar_number || validationErrors.aadhar_number ? 'border-red-500' : ''}
              />
              {liveErrors.aadhar_number && (
                <p className="text-sm text-red-500">{liveErrors.aadhar_number}</p>
              )}
              {validationErrors.aadhar_number && (
                <p className="text-sm text-red-500">{validationErrors.aadhar_number}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="pan_card_number">PAN Card Number</Label>
              <Input
                id="pan_card_number"
                value={newClient.pan_card_number}
                onChange={(e) => {
                  const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').slice(0, 10);
                  setNewClient(prev => ({ ...prev, pan_card_number: value }));
                  validateField('pan_card_number', value);
                }}
                onBlur={(e) => handleFieldBlur('pan_card_number', e.target.value)}
                placeholder="********** (optional)"
                maxLength={10}
                className={liveErrors.pan_card_number || validationErrors.pan_card_number ? 'border-red-500' : ''}
              />
              {liveErrors.pan_card_number && (
                <p className="text-sm text-red-500">{liveErrors.pan_card_number}</p>
              )}
              {validationErrors.pan_card_number && (
                <p className="text-sm text-red-500">{validationErrors.pan_card_number}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="village">Village</Label>
              <Input
                id="village"
                value={newClient.village}
                onChange={(e) => setNewClient(prev => ({ ...prev, village: e.target.value }))}
                placeholder="Enter village name (optional)"
              />
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowAddModal(false);
                  setNewClient({
                    first_name: '',
                    last_name: '',
                    mobile_number: '',
                    cif_id: '',
                    aadhar_number: '',
                    pan_card_number: '',
                    village: ''
                  });
                  setLiveErrors({});
                  setValidationErrors({});
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={addingClient || Object.keys(liveErrors).length > 0 || Object.keys(validationErrors).length > 0}
              >
                {addingClient ? 'Adding...' : 'Add Client'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );

  async function handleAddClient(e: React.FormEvent) {
    e.preventDefault();

    // Validate required fields
    const newLiveErrors: Record<string, string> = {};

    if (!newClient.first_name.trim()) {
      newLiveErrors.first_name = 'First name is required';
    }
    if (!newClient.mobile_number.trim()) {
      newLiveErrors.mobile_number = 'Mobile number is required';
    } else if (!/^\d{10}$/.test(newClient.mobile_number)) {
      newLiveErrors.mobile_number = 'Mobile number must be exactly 10 digits';
    }
    if (!newClient.cif_id.trim()) {
      newLiveErrors.cif_id = 'CIF ID is required';
    }

    // Check for validation errors
    if (Object.keys(newLiveErrors).length > 0 || Object.keys(validationErrors).length > 0) {
      setLiveErrors(newLiveErrors);
      toast({
        title: "Validation Error",
        description: "Please fix all errors before submitting",
        variant: "destructive",
      });
      return;
    }

    setAddingClient(true);

    try {
      const { data, error } = await supabase
        .from('clients')
        .insert({
          first_name: newClient.first_name,
          last_name: newClient.last_name,
          mobile_number: newClient.mobile_number,
          cif_id: newClient.cif_id || null,
          aadhar_number: newClient.aadhar_number || null,
          pan_card_number: newClient.pan_card_number || null,
          village: newClient.village || null,
          is_deleted: false
        })
        .select('id, first_name, last_name, email, mobile_number, village, cif_id')
        .single();

      if (error) {
        const message = error.message || '';
        let customError = "Client creation failed";

        if (message.includes('clients_mobile_number_key')) {
          customError = "This mobile number already exists";
        } else if (message.includes('clients_aadhar_number_key')) {
          customError = "This Aadhar number already exists";
        } else if (message.includes('clients_pan_card_number_key')) {
          customError = "This PAN card number already exists";
        } else if (message.includes('clients_cif_id_key')) {
          customError = "This CIF ID already exists";
        }

        throw new Error(customError);
      }

      // Add to clients list
      setClients(prev => [...prev, data]);

      // Select the new client
      onClientSelect(data);

      // Close modal and reset form
      setShowAddModal(false);
      setNewClient({
        first_name: '',
        last_name: '',
        mobile_number: '',
        cif_id: '',
        aadhar_number: '',
        pan_card_number: '',
        village: ''
      });
      setLiveErrors({});
      setValidationErrors({});

      toast({
        title: "Success",
        description: "Client added successfully",
      });
    } catch (error) {
      console.error('Error adding client:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add client",
        variant: "destructive",
      });
    } finally {
      setAddingClient(false);
    }
  }
};

export default ClientSelector;