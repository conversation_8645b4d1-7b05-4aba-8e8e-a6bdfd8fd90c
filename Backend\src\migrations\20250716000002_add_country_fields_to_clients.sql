-- Add country code and country fields to clients table
ALTER TABLE public.clients 
ADD COLUMN country_code VARCHAR(5) DEFAULT '+91',
ADD COLUMN country VARCHAR(100) DEFAULT 'India';

-- Add indexes for better performance on country-related queries
CREATE INDEX idx_clients_country_code ON public.clients(country_code);
CREATE INDEX idx_clients_country ON public.clients(country);

-- Add comments to document the new fields
COMMENT ON COLUMN public.clients.country_code IS 'Country code for mobile number (e.g., +91 for India)';
COMMENT ON COLUMN public.clients.country IS 'Country name where the client is located';

-- Update existing records to have default values
UPDATE public.clients 
SET country_code = '+91', country = 'India' 
WHERE country_code IS NULL OR country IS NULL;
